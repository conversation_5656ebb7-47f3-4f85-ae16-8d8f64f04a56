version: 6
environments:
  default:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f5/10/6c25ed6de94c49f88a91fa5018cb4c0f3625f31d5be9f771ebe5cc7cd506/aiosqlite-0.21.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3d/bd/35b5691919859a906cd098d62e2a1399cb8ede3e05b6480cf15d472ad5b1/apscheduler-4.0.0a6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/14/4b/ead00905132820b623732b175d66354e9d3e69fcf2a5dcdab780664e7896/google_api_core-2.25.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/b8/2800f1aee399ea325fa675244de5178ab938485506ded2d17acf73e23cdb/google_api_python_client-2.175.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/8a/fe34d2f3f9470a27b01c9e76226965863f153d5fbe276f83608562e49c04/google_auth_httplib2-0.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ac/84/40ee070be95771acd2f4418981edb834979424565c3eec3cd88b6aa09d24/google_auth_oauthlib-1.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/f2/ad51331a157c7015c675702e2d5230c243695c788f8f75feba1af32b3617/greenlet-3.2.3-cp313-cp313-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a8/6c/d2fbdaaa5959339d53ba38e94c123e4e84b8fbc4b84beb0e70d7c1608486/httplib2-0.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d5/1c/a2a29649c0b1983d3ef57ee87a66487fdeb45132df66ab30dd37f7dbe162/pillow-11.3.0-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/4e/6d/280c4c2ce28b1593a19ad5239c8b826871fc6ec275c21afc8e1820108039/proto_plus-1.26.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fa/b1/b59d405d64d31999244643d88c45c8241c58f17cc887e73bcb90602327f8/protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/3c/f4abd740877a35abade05e437245b192f9d0ffb48bbbbd708df33d3cda37/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/58/f0/427018098906416f580e3cf1366d3b1abfb408a0652e9f31600c24a1903c/pydantic_settings-2.10.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/05/e7/df2285f3d08fee213f2d041540fa4fc9ca6c2d44cf36d3a035bf2a8d2bcc/pyparsing-3.2.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5e/51/5ba9ea3246ea068630acf35a6ba0d181e99f1af1afd17e159eac7e8bc2b8/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/4a/97ee6973e3a73c74c8120d59829c3861ea52210667ec3e7a16045c62b64d/structlog-25.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/30/643397144bfbfec6f6ef821f36f33e57d35946c44a2352d3c9f0ae847619/tenacity-9.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a9/99/3ae339466c9183ea5b8ae87b34c0b897eda475d2aec2307cae60e5cd4f29/uritemplate-4.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ffmpeg-7.1.1-gpl_h30b7fc1_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fribidi-1.0.10-hb9de7d4_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gdk-pixbuf-2.42.12-ha61d561_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.44-h5e2c951_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.25.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.25.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libass-0.17.3-h3c9f632_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libhwloc-2.11.2-default_h2c612a5_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-arm-cpu-plugin-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-batch-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-hetero-plugin-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-ir-frontend-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-onnx-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-paddle-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-pytorch-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-frontend-2025.0.0-h33e842c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.50-hec79eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/librsvg-2.58.4-h3ac5bce_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.2-he2a92bd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvpx-1.14.1-h0a1ffab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openh264-2.6.0-h0564a2a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.1-hd08dc88_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pango-1.56.4-he55ef5b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pugixml-1.15-h6ef32b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/snappy-1.2.1-hd4fb6f5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tbb-2022.1.0-hf6e3e71_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.24.0-h698ed42_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x264-1!164.3095-h4e544f5_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x265-3.5-hdd96247_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/7f/32ca0f170496aa2ab9b812630fac0c2372c531b797e1deb3deb4cea904bd/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/fb/76/641ae371508676492379f16e2fa48f4e2c11741bd63c48be4b12a6b09cba/aiosignal-1.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f5/10/6c25ed6de94c49f88a91fa5018cb4c0f3625f31d5be9f771ebe5cc7cd506/aiosqlite-0.21.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3d/bd/35b5691919859a906cd098d62e2a1399cb8ede3e05b6480cf15d472ad5b1/apscheduler-4.0.0a6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/19/7c/71bb0bbe0832793c601fff68cd0cf6143753d0c667f9aec93d3c323f4b55/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/14/4b/ead00905132820b623732b175d66354e9d3e69fcf2a5dcdab780664e7896/google_api_core-2.25.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/b8/2800f1aee399ea325fa675244de5178ab938485506ded2d17acf73e23cdb/google_api_python_client-2.175.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/8a/fe34d2f3f9470a27b01c9e76226965863f153d5fbe276f83608562e49c04/google_auth_httplib2-0.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ac/84/40ee070be95771acd2f4418981edb834979424565c3eec3cd88b6aa09d24/google_auth_oauthlib-1.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/ae/91a957ba60482d3fecf9be49bc3948f341d706b52ddb9d83a70d42abd498/greenlet-3.2.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a8/6c/d2fbdaaa5959339d53ba38e94c123e4e84b8fbc4b84beb0e70d7c1608486/httplib2-0.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e7/ab/320d8507e7726c460cb77117848b3834ea0d59e769f36fdae495f7669929/multidict-6.6.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ba/c9/09e6746630fe6372c67c648ff9deae52a2bc20897d51fa293571977ceb5d/pillow-11.3.0-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/5b/ad/3f0f9a705fb630d175146cd7b1d2bf5555c9beaed54e94132b21aac098a6/propcache-0.3.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/4e/6d/280c4c2ce28b1593a19ad5239c8b826871fc6ec275c21afc8e1820108039/proto_plus-1.26.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/a1/7a5a94032c83375e4fe7e7f56e3976ea6ac90c5e85fac8576409e25c39c3/protobuf-6.31.1-cp39-abi3-manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/5e/a0a7b8885c98889a18b6e376f344da1ef323d270b44edf8174d6bce4d622/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/58/f0/427018098906416f580e3cf1366d3b1abfb408a0652e9f31600c24a1903c/pydantic_settings-2.10.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/05/e7/df2285f3d08fee213f2d041540fa4fc9ca6c2d44cf36d3a035bf2a8d2bcc/pyparsing-3.2.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/72/c97ad430f0b0e78efaf2791342e13ffeafcbb3c06242f01a3bb8fe44f65d/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/4a/97ee6973e3a73c74c8120d59829c3861ea52210667ec3e7a16045c62b64d/structlog-25.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/30/643397144bfbfec6f6ef821f36f33e57d35946c44a2352d3c9f0ae847619/tenacity-9.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b5/00/d631e67a838026495268c2f6884f3711a15a9a2a96cd244fdaea53b823fb/typing_extensions-4.14.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a9/99/3ae339466c9183ea5b8ae87b34c0b897eda475d2aec2307cae60e5cd4f29/uritemplate-4.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a3/25/35afe384e31115a1a801fbcf84012d7a066d89035befae7c5d4284df1e03/yarl-1.20.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  dev:
    channels:
    - url: https://conda.anaconda.org/conda-forge/
    indexes:
    - https://pypi.org/simple
    packages:
      linux-64:
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f5/10/6c25ed6de94c49f88a91fa5018cb4c0f3625f31d5be9f771ebe5cc7cd506/aiosqlite-0.21.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3d/bd/35b5691919859a906cd098d62e2a1399cb8ede3e05b6480cf15d472ad5b1/apscheduler-4.0.0a6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/56/13ab06b4f93ca7cac71078fbe37fcea175d3216f31f85c3168a6bbd0bb9a/flake8-7.3.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/14/4b/ead00905132820b623732b175d66354e9d3e69fcf2a5dcdab780664e7896/google_api_core-2.25.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/b8/2800f1aee399ea325fa675244de5178ab938485506ded2d17acf73e23cdb/google_api_python_client-2.175.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/8a/fe34d2f3f9470a27b01c9e76226965863f153d5fbe276f83608562e49c04/google_auth_httplib2-0.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ac/84/40ee070be95771acd2f4418981edb834979424565c3eec3cd88b6aa09d24/google_auth_oauthlib-1.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/f2/ad51331a157c7015c675702e2d5230c243695c788f8f75feba1af32b3617/greenlet-3.2.3-cp313-cp313-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a8/6c/d2fbdaaa5959339d53ba38e94c123e4e84b8fbc4b84beb0e70d7c1608486/httplib2-0.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/b4/7e/81ca3b074021ad9775e5cb97ebe0089c0f13684b066a750b7dc208438403/mypy-1.16.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d5/1c/a2a29649c0b1983d3ef57ee87a66487fdeb45132df66ab30dd37f7dbe162/pillow-11.3.0-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/74/a88bf1b1efeae488a0c0b7bdf71429c313722d1fc0f377537fbe554e6180/pre_commit-4.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/4e/6d/280c4c2ce28b1593a19ad5239c8b826871fc6ec275c21afc8e1820108039/proto_plus-1.26.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/fa/b1/b59d405d64d31999244643d88c45c8241c58f17cc887e73bcb90602327f8/protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d7/27/a58ddaf8c588a3ef080db9d0b7e0b97215cee3a45df74f3a94dbbf5c893a/pycodestyle-2.14.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/eb/3c/f4abd740877a35abade05e437245b192f9d0ffb48bbbbd708df33d3cda37/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/58/f0/427018098906416f580e3cf1366d3b1abfb408a0652e9f31600c24a1903c/pydantic_settings-2.10.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/2f/81d580a0fb83baeb066698975cb14a618bdbed7720678566f1b046a95fe8/pyflakes-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/05/e7/df2285f3d08fee213f2d041540fa4fc9ca6c2d44cf36d3a035bf2a8d2bcc/pyparsing-3.2.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/30/05/ce271016e351fddc8399e546f6e23761967ee09c8c568bbfbecb0c150171/pytest_asyncio-1.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5e/51/5ba9ea3246ea068630acf35a6ba0d181e99f1af1afd17e159eac7e8bc2b8/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      - pypi: https://files.pythonhosted.org/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/4a/97ee6973e3a73c74c8120d59829c3861ea52210667ec3e7a16045c62b64d/structlog-25.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/30/643397144bfbfec6f6ef821f36f33e57d35946c44a2352d3c9f0ae847619/tenacity-9.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a9/99/3ae339466c9183ea5b8ae87b34c0b897eda475d2aec2307cae60e5cd4f29/uritemplate-4.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/40/b1c265d4b2b62b58576588510fc4d1fe60a86319c8de99fd8e9fec617d2c/virtualenv-20.31.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
      linux-aarch64:
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ffmpeg-7.1.1-gpl_h30b7fc1_906.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fribidi-1.0.10-hb9de7d4_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gdk-pixbuf-2.42.12-ha61d561_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.44-h5e2c951_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.25.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.25.1-h5e0f5ae_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libass-0.17.3-h3c9f632_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.25.1-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libhwloc-2.11.2-default_h2c612a5_1001.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-arm-cpu-plugin-2025.0.0-hd63d6c0_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-batch-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-plugin-2025.0.0-hf15766e_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-hetero-plugin-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-ir-frontend-2025.0.0-ha8e9e04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-onnx-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-paddle-frontend-2025.0.0-hd8f0270_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-pytorch-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-frontend-2025.0.0-h33e842c_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5ad3122_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.50-hec79eb8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/librsvg-2.58.4-h3ac5bce_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.2-he2a92bd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvpx-1.14.1-h0a1ffab_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openh264-2.6.0-h0564a2a_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.1-hd08dc88_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pango-1.56.4-he55ef5b_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pugixml-1.15-h6ef32b0_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/snappy-1.2.1-hd4fb6f5_1.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tbb-2022.1.0-hf6e3e71_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
      - conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.24.0-h698ed42_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x264-1!164.3095-h4e544f5_2.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x265-3.5-hdd96247_3.tar.bz2
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
      - conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
      - pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/26/7f/32ca0f170496aa2ab9b812630fac0c2372c531b797e1deb3deb4cea904bd/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/fb/76/641ae371508676492379f16e2fa48f4e2c11741bd63c48be4b12a6b09cba/aiosignal-1.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f5/10/6c25ed6de94c49f88a91fa5018cb4c0f3625f31d5be9f771ebe5cc7cd506/aiosqlite-0.21.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3d/bd/35b5691919859a906cd098d62e2a1399cb8ede3e05b6480cf15d472ad5b1/apscheduler-4.0.0a6-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/09/71/54e999902aed72baf26bca0d50781b01838251a462612966e9fc4891eadd/black-25.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/9f/56/13ab06b4f93ca7cac71078fbe37fcea175d3216f31f85c3168a6bbd0bb9a/flake8-7.3.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/19/7c/71bb0bbe0832793c601fff68cd0cf6143753d0c667f9aec93d3c323f4b55/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/14/4b/ead00905132820b623732b175d66354e9d3e69fcf2a5dcdab780664e7896/google_api_core-2.25.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f8/b8/2800f1aee399ea325fa675244de5178ab938485506ded2d17acf73e23cdb/google_api_python_client-2.175.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/8a/fe34d2f3f9470a27b01c9e76226965863f153d5fbe276f83608562e49c04/google_auth_httplib2-0.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ac/84/40ee070be95771acd2f4418981edb834979424565c3eec3cd88b6aa09d24/google_auth_oauthlib-1.2.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/48/ae/91a957ba60482d3fecf9be49bc3948f341d706b52ddb9d83a70d42abd498/greenlet-3.2.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a8/6c/d2fbdaaa5959339d53ba38e94c123e4e84b8fbc4b84beb0e70d7c1608486/httplib2-0.22.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e7/ab/320d8507e7726c460cb77117848b3834ea0d59e769f36fdae495f7669929/multidict-6.6.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/c9/4f/c3c6b4b66374b5f68bab07c8cabd63a049ff69796b844bc759a0ca99bb2a/mypy-1.16.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/ba/c9/09e6746630fe6372c67c648ff9deae52a2bc20897d51fa293571977ceb5d/pillow-11.3.0-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/88/74/a88bf1b1efeae488a0c0b7bdf71429c313722d1fc0f377537fbe554e6180/pre_commit-4.2.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5b/ad/3f0f9a705fb630d175146cd7b1d2bf5555c9beaed54e94132b21aac098a6/propcache-0.3.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/4e/6d/280c4c2ce28b1593a19ad5239c8b826871fc6ec275c21afc8e1820108039/proto_plus-1.26.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/a1/7a5a94032c83375e4fe7e7f56e3976ea6ac90c5e85fac8576409e25c39c3/protobuf-6.31.1-cp39-abi3-manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/d7/27/a58ddaf8c588a3ef080db9d0b7e0b97215cee3a45df74f3a94dbbf5c893a/pycodestyle-2.14.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/6f/5e/a0a7b8885c98889a18b6e376f344da1ef323d270b44edf8174d6bce4d622/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/58/f0/427018098906416f580e3cf1366d3b1abfb408a0652e9f31600c24a1903c/pydantic_settings-2.10.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/2f/81d580a0fb83baeb066698975cb14a618bdbed7720678566f1b046a95fe8/pyflakes-3.4.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/05/e7/df2285f3d08fee213f2d041540fa4fc9ca6c2d44cf36d3a035bf2a8d2bcc/pyparsing-3.2.3-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/30/05/ce271016e351fddc8399e546f6e23761967ee09c8c568bbfbecb0c150171/pytest_asyncio-1.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/7c/9a/337322f27005c33bcb656c655fa78325b730324c78620e8328ae28b64d0c/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/72/c97ad430f0b0e78efaf2791342e13ffeafcbb3c06242f01a3bb8fe44f65d/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
      - pypi: https://files.pythonhosted.org/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a0/4a/97ee6973e3a73c74c8120d59829c3861ea52210667ec3e7a16045c62b64d/structlog-25.4.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/e5/30/643397144bfbfec6f6ef821f36f33e57d35946c44a2352d3c9f0ae847619/tenacity-9.1.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/b5/00/d631e67a838026495268c2f6884f3711a15a9a2a96cd244fdaea53b823fb/typing_extensions-4.14.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a9/99/3ae339466c9183ea5b8ae87b34c0b897eda475d2aec2307cae60e5cd4f29/uritemplate-4.2.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/f3/40/b1c265d4b2b62b58576588510fc4d1fe60a86319c8de99fd8e9fec617d2c/virtualenv-20.31.2-py3-none-any.whl
      - pypi: https://files.pythonhosted.org/packages/a3/25/35afe384e31115a1a801fbcf84012d7a066d89035befae7c5d4284df1e03/yarl-1.20.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
packages:
- conda: https://conda.anaconda.org/conda-forge/linux-64/_libgcc_mutex-0.1-conda_forge.tar.bz2
  sha256: fe51de6107f9edc7aa4f786a70f4a883943bc9d39b3bb7307c04c41410990726
  md5: d7c89558ba9fa0495403155b64376d81
  license: None
  purls: []
  size: 2562
  timestamp: 1578324546067
- conda: https://conda.anaconda.org/conda-forge/linux-64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: fbe2c5e56a653bebb982eda4876a9178aedfc2b545f25d0ce9c4c0b508253d22
  md5: 73aaf86a425cc6e73fcf236a5a46396d
  depends:
  - _libgcc_mutex 0.1 conda_forge
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23621
  timestamp: 1650670423406
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/_openmp_mutex-4.5-2_gnu.tar.bz2
  build_number: 16
  sha256: 3702bef2f0a4d38bd8288bbe54aace623602a1343c2cfbefd3fa188e015bebf0
  md5: 6168d71addc746e8f2b8d57dfd2edcea
  depends:
  - libgomp >=7.5.0
  constrains:
  - openmp_impl 9999
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 23712
  timestamp: 1650670790230
- pypi: https://files.pythonhosted.org/packages/a5/45/30bb92d442636f570cb5651bc661f52b610e2eec3f891a5dc3a4c3667db0/aiofiles-24.1.0-py3-none-any.whl
  name: aiofiles
  version: 24.1.0
  sha256: b4ec55f4195e3eb5d7abd1bf7e061763e864dd4954231fb8539a0ef8bb8260e5
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/0f/15/5bf3b99495fb160b63f95972b81750f18f7f4e02ad051373b669d17d44f2/aiohappyeyeballs-2.6.1-py3-none-any.whl
  name: aiohappyeyeballs
  version: 2.6.1
  sha256: f349ba8f4b75cb25c99c5c2d84e997e485204d2902a9597802b0371f09331fb8
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/26/7f/32ca0f170496aa2ab9b812630fac0c2372c531b797e1deb3deb4cea904bd/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: aiohttp
  version: 3.12.13
  sha256: ad7c8e5c25f2a26842a7c239de3f7b6bfb92304593ef997c04ac49fb703ff4d7
  requires_dist:
  - aiohappyeyeballs>=2.5.0
  - aiosignal>=1.1.2
  - async-timeout>=4.0,<6.0 ; python_full_version < '3.11'
  - attrs>=17.3.0
  - frozenlist>=1.1.1
  - multidict>=4.5,<7.0
  - propcache>=0.2.0
  - yarl>=1.17.0,<2.0
  - aiodns>=3.3.0 ; extra == 'speedups'
  - brotli ; platform_python_implementation == 'CPython' and extra == 'speedups'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'speedups'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/48/19/0377df97dd0176ad23cd8cad4fd4232cfeadcec6c1b7f036315305c98e3f/aiohttp-3.12.13-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: aiohttp
  version: 3.12.13
  sha256: 7a0b9170d5d800126b5bc89d3053a2363406d6e327afb6afaeda2d19ee8bb103
  requires_dist:
  - aiohappyeyeballs>=2.5.0
  - aiosignal>=1.1.2
  - async-timeout>=4.0,<6.0 ; python_full_version < '3.11'
  - attrs>=17.3.0
  - frozenlist>=1.1.1
  - multidict>=4.5,<7.0
  - propcache>=0.2.0
  - yarl>=1.17.0,<2.0
  - aiodns>=3.3.0 ; extra == 'speedups'
  - brotli ; platform_python_implementation == 'CPython' and extra == 'speedups'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'speedups'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/ec/6a/bc7e17a3e87a2985d3e8f4da4cd0f481060eb78fb08596c42be62c90a4d9/aiosignal-1.3.2-py2.py3-none-any.whl
  name: aiosignal
  version: 1.3.2
  sha256: 45cde58e409a301715980c2b01d0c28bdde3770d8290b5eb2173759d9acb31a5
  requires_dist:
  - frozenlist>=1.1.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/fb/76/641ae371508676492379f16e2fa48f4e2c11741bd63c48be4b12a6b09cba/aiosignal-1.4.0-py3-none-any.whl
  name: aiosignal
  version: 1.4.0
  sha256: 053243f8b92b990551949e63930a839ff0cf0b0ebbe0597b0f3fb19e1a0fe82e
  requires_dist:
  - frozenlist>=1.1.0
  - typing-extensions>=4.2 ; python_full_version < '3.13'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/f5/10/6c25ed6de94c49f88a91fa5018cb4c0f3625f31d5be9f771ebe5cc7cd506/aiosqlite-0.21.0-py3-none-any.whl
  name: aiosqlite
  version: 0.21.0
  sha256: 2549cf4057f95f53dcba16f2b64e8e2791d7e1adedb13197dd8ed77bb226d7d0
  requires_dist:
  - typing-extensions>=4.0
  - attribution==1.7.1 ; extra == 'dev'
  - black==24.3.0 ; extra == 'dev'
  - build>=1.2 ; extra == 'dev'
  - coverage[toml]==7.6.10 ; extra == 'dev'
  - flake8==7.0.0 ; extra == 'dev'
  - flake8-bugbear==24.12.12 ; extra == 'dev'
  - flit==3.10.1 ; extra == 'dev'
  - mypy==1.14.1 ; extra == 'dev'
  - ufmt==2.5.1 ; extra == 'dev'
  - usort==1.0.8.post1 ; extra == 'dev'
  - sphinx==8.1.3 ; extra == 'docs'
  - sphinx-mdinclude==0.6.1 ; extra == 'docs'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/alsa-lib-1.2.14-hb9d3cd8_0.conda
  sha256: b9214bc17e89bf2b691fad50d952b7f029f6148f4ac4fe7c60c08f093efdf745
  md5: 76df83c2a9035c54df5d04ff81bcc02d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  purls: []
  size: 566531
  timestamp: 1744668655747
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/alsa-lib-1.2.14-h86ecc28_0.conda
  sha256: 0aa836f6dd9132f243436898ed8024f408910f65220bafbfc95f71ab829bb395
  md5: a696b24c1b473ecc4774bcb5a6ac6337
  depends:
  - libgcc >=13
  license: LGPL-2.1-or-later
  license_family: GPL
  purls: []
  size: 595290
  timestamp: 1744668754404
- pypi: https://files.pythonhosted.org/packages/78/b6/6307fbef88d9b5ee7421e68d78a9f162e0da4900bc5f5793f6d3d0e34fb8/annotated_types-0.7.0-py3-none-any.whl
  name: annotated-types
  version: 0.7.0
  sha256: 1f02e8b43a8fbbc3f3e0d4f0f4bfc8131bcb4eebe8849b8e5c773f3a1c582a53
  requires_dist:
  - typing-extensions>=4.0.0 ; python_full_version < '3.9'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/a1/ee/48ca1a7c89ffec8b6a0c5d02b89c305671d5ffd8d3c94acf8b8c408575bb/anyio-4.9.0-py3-none-any.whl
  name: anyio
  version: 4.9.0
  sha256: 9f76d541cad6e36af7beb62e978876f3b41e3e04f2c1fbf0884604c0a9c4d93c
  requires_dist:
  - exceptiongroup>=1.0.2 ; python_full_version < '3.11'
  - idna>=2.8
  - sniffio>=1.1
  - typing-extensions>=4.5 ; python_full_version < '3.13'
  - trio>=0.26.1 ; extra == 'trio'
  - anyio[trio] ; extra == 'test'
  - blockbuster>=1.5.23 ; extra == 'test'
  - coverage[toml]>=7 ; extra == 'test'
  - exceptiongroup>=1.2.0 ; extra == 'test'
  - hypothesis>=4.0 ; extra == 'test'
  - psutil>=5.9 ; extra == 'test'
  - pytest>=7.0 ; extra == 'test'
  - trustme ; extra == 'test'
  - truststore>=0.9.1 ; python_full_version >= '3.10' and extra == 'test'
  - uvloop>=0.21 ; python_full_version < '3.14' and platform_python_implementation == 'CPython' and sys_platform != 'win32' and extra == 'test'
  - packaging ; extra == 'doc'
  - sphinx~=8.2 ; extra == 'doc'
  - sphinx-rtd-theme ; extra == 'doc'
  - sphinx-autodoc-typehints>=1.2.0 ; extra == 'doc'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/aom-3.9.1-hac33072_0.conda
  sha256: b08ef033817b5f9f76ce62dfcac7694e7b6b4006420372de22494503decac855
  md5: 346722a0be40f6edc53f12640d301338
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2706396
  timestamp: 1718551242397
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/aom-3.9.1-hcccb83c_0.conda
  sha256: ac438ce5d3d3673a9188b535fc7cda413b479f0d52536aeeac1bd82faa656ea0
  md5: cc744ac4efe5bcaa8cca51ff5b850df0
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 3250813
  timestamp: 1718551360260
- pypi: https://files.pythonhosted.org/packages/3d/bd/35b5691919859a906cd098d62e2a1399cb8ede3e05b6480cf15d472ad5b1/apscheduler-4.0.0a6-py3-none-any.whl
  name: apscheduler
  version: 4.0.0a6
  sha256: 87031f7537aaa6ea2d3e69fdd9454b641f18938fc8cd0c589972006eceba8ee9
  requires_dist:
  - anyio~=4.0
  - attrs>=22.1
  - tenacity>=8.0,<10.0
  - tzlocal>=3.0
  - typing-extensions>=4.0 ; python_full_version < '3.11'
  - asyncpg>=0.20 ; extra == 'asyncpg'
  - cbor2>=5.0 ; extra == 'cbor'
  - pymongo>=4 ; extra == 'mongodb'
  - paho-mqtt>=2.0 ; extra == 'mqtt'
  - redis>=5.0.1 ; extra == 'redis'
  - sqlalchemy[asyncio]>=2.0.24 ; extra == 'sqlalchemy'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/attr-2.5.1-h166bdaf_1.tar.bz2
  sha256: 82c13b1772c21fc4a17441734de471d3aabf82b61db9b11f4a1bd04a9c4ac324
  md5: d9c69a24ad678ffce24c6543a0176b00
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 71042
  timestamp: 1660065501192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/attr-2.5.1-h4e544f5_1.tar.bz2
  sha256: 2c793b48e835a8fac93f1664c706442972a0206963bf8ca202e83f7f4d29a7d7
  md5: 1ef6c06fec1b6f5ee99ffe2152e53568
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 74992
  timestamp: 1660065534958
- pypi: https://files.pythonhosted.org/packages/77/06/bb80f5f86020c4551da315d78b3ab75e8228f89f0162f2c3a819e407941a/attrs-25.3.0-py3-none-any.whl
  name: attrs
  version: 25.3.0
  sha256: 427318ce031701fea540783410126f03899a97ffc6f61596ad581ac2e40e3bc3
  requires_dist:
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'benchmark'
  - hypothesis ; extra == 'benchmark'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pympler ; extra == 'benchmark'
  - pytest-codspeed ; extra == 'benchmark'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'benchmark'
  - pytest-xdist[psutil] ; extra == 'benchmark'
  - pytest>=4.3.0 ; extra == 'benchmark'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'cov'
  - coverage[toml]>=5.3 ; extra == 'cov'
  - hypothesis ; extra == 'cov'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pympler ; extra == 'cov'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'cov'
  - pytest-xdist[psutil] ; extra == 'cov'
  - pytest>=4.3.0 ; extra == 'cov'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'dev'
  - hypothesis ; extra == 'dev'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pre-commit-uv ; extra == 'dev'
  - pympler ; extra == 'dev'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'dev'
  - pytest-xdist[psutil] ; extra == 'dev'
  - pytest>=4.3.0 ; extra == 'dev'
  - cogapp ; extra == 'docs'
  - furo ; extra == 'docs'
  - myst-parser ; extra == 'docs'
  - sphinx ; extra == 'docs'
  - sphinx-notfound-page ; extra == 'docs'
  - sphinxcontrib-towncrier ; extra == 'docs'
  - towncrier ; extra == 'docs'
  - cloudpickle ; platform_python_implementation == 'CPython' and extra == 'tests'
  - hypothesis ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pympler ; extra == 'tests'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests'
  - pytest-xdist[psutil] ; extra == 'tests'
  - pytest>=4.3.0 ; extra == 'tests'
  - mypy>=1.11.1 ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  - pytest-mypy-plugins ; python_full_version >= '3.10' and platform_python_implementation == 'CPython' and extra == 'tests-mypy'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/09/71/54e999902aed72baf26bca0d50781b01838251a462612966e9fc4891eadd/black-25.1.0-py3-none-any.whl
  name: black
  version: 25.1.0
  sha256: 95e8176dae143ba9097f351d174fdaf0ccd29efb414b362ae3fd72bf0f710717
  requires_dist:
  - click>=8.0.0
  - mypy-extensions>=0.4.3
  - packaging>=22.0
  - pathspec>=0.9.0
  - platformdirs>=2
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - typing-extensions>=4.0.1 ; python_full_version < '3.11'
  - colorama>=0.4.3 ; extra == 'colorama'
  - aiohttp>=3.10 ; extra == 'd'
  - ipython>=7.8.0 ; extra == 'jupyter'
  - tokenize-rt>=3.2.0 ; extra == 'jupyter'
  - uvloop>=0.15.2 ; extra == 'uvloop'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/e3/ee/adda3d46d4a9120772fae6de454c8495603c37c4c3b9c60f25b1ab6401fe/black-25.1.0-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
  name: black
  version: 25.1.0
  sha256: 030b9759066a4ee5e5aca28c3c77f9c64789cdd4de8ac1df642c40b708be6171
  requires_dist:
  - click>=8.0.0
  - mypy-extensions>=0.4.3
  - packaging>=22.0
  - pathspec>=0.9.0
  - platformdirs>=2
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - typing-extensions>=4.0.1 ; python_full_version < '3.11'
  - colorama>=0.4.3 ; extra == 'colorama'
  - aiohttp>=3.10 ; extra == 'd'
  - ipython>=7.8.0 ; extra == 'jupyter'
  - tokenize-rt>=3.2.0 ; extra == 'jupyter'
  - uvloop>=0.15.2 ; extra == 'uvloop'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/bzip2-1.0.8-h4bc722e_7.conda
  sha256: 5ced96500d945fb286c9c838e54fa759aa04a7129c59800f0846b4335cee770d
  md5: 62ee74e96c5ebb0af99386de58cf9553
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 252783
  timestamp: 1720974456583
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/bzip2-1.0.8-h68df207_7.conda
  sha256: 2258b0b33e1cb3a9852d47557984abb6e7ea58e3d7f92706ec1f8e879290c4cb
  md5: 56398c28220513b9ea13d7b450acfb20
  depends:
  - libgcc-ng >=12
  license: bzip2-1.0.6
  license_family: BSD
  purls: []
  size: 189884
  timestamp: 1720974504976
- conda: https://conda.anaconda.org/conda-forge/noarch/ca-certificates-2025.6.15-hbd8a1cb_0.conda
  sha256: 7cfec9804c84844ea544d98bda1d9121672b66ff7149141b8415ca42dfcd44f6
  md5: 72525f07d72806e3b639ad4504c30ce5
  depends:
  - __unix
  license: ISC
  purls: []
  size: 151069
  timestamp: 1749990087500
- pypi: https://files.pythonhosted.org/packages/72/76/20fa66124dbe6be5cafeb312ece67de6b61dd91a0247d1ea13db4ebb33c2/cachetools-5.5.2-py3-none-any.whl
  name: cachetools
  version: 5.5.2
  sha256: d26a22bcc62eb95c3beabd9f1ee5e820d3d2704fe2967cbe350e20c8ffcd3f0a
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/cairo-1.18.4-h3394656_0.conda
  sha256: 3bd6a391ad60e471de76c0e9db34986c4b5058587fbf2efa5a7f54645e28c2c7
  md5: 09262e66b19567aff4f592fb53b28760
  depends:
  - __glibc >=2.17,<3.0.a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 978114
  timestamp: 1741554591855
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/cairo-1.18.4-h83712da_0.conda
  sha256: 37cfff940d2d02259afdab75eb2dbac42cf830adadee78d3733d160a1de2cc66
  md5: cd55953a67ec727db5dc32b167201aa6
  depends:
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.12.1,<3.0a0
  - icu >=75.1,<76.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pixman >=0.44.2,<1.0a0
  - xorg-libice >=1.1.2,<2.0a0
  - xorg-libsm >=1.2.5,<2.0a0
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxrender >=0.9.12,<0.10.0a0
  license: LGPL-2.1-only or MPL-1.1
  purls: []
  size: 966667
  timestamp: 1741554768968
- pypi: https://files.pythonhosted.org/packages/84/ae/320161bd181fc06471eed047ecce67b693fd7515b16d495d8932db763426/certifi-2025.6.15-py3-none-any.whl
  name: certifi
  version: 2025.6.15
  sha256: 2e0c7ce7cb5d8f8634ca55d2ba7e6ec2689a2fd6537d8dec1296a477a4910057
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/c5/55/51844dd50c4fc7a33b653bfaba4c2456f06955289ca770a5dbd5fd267374/cfgv-3.4.0-py2.py3-none-any.whl
  name: cfgv
  version: 3.4.0
  sha256: b7265b1f29fd3316bfcd2b330d63d024f2bfd8bcb8b0272f8e19a504856c48f9
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/04/93/bf204e6f344c39d9937d3c13c8cd5bbfc266472e51fc8c07cb7f64fcd2de/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: charset-normalizer
  version: 3.4.2
  sha256: eba9904b0f38a143592d9fc0e19e2df0fa2e41c3c3745554761c5f6447eedabf
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/e2/28/ffc026b26f441fc67bd21ab7f03b313ab3fe46714a14b516f931abe1a2d8/charset_normalizer-3.4.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: charset-normalizer
  version: 3.4.2
  sha256: 6c9379d65defcab82d07b2a9dfbfc2e95bc8fe0ebb1b176a3190230a3ef0e07c
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/85/32/10bb5764d90a8eee674e9dc6f4db6a0ab47c8c4d0d83c27f7c39ac415a4d/click-8.2.1-py3-none-any.whl
  name: click
  version: 8.2.1
  sha256: 61a3265b914e850b85317d0b3109c7f8cd35a670f963866005d6ef1d5175a12b
  requires_dist:
  - colorama ; sys_platform == 'win32'
  requires_python: '>=3.10'
- conda: https://conda.anaconda.org/conda-forge/linux-64/dav1d-1.2.1-hd590300_0.conda
  sha256: 22053a5842ca8ee1cf8e1a817138cdb5e647eb2c46979f84153f6ad7bde73020
  md5: 418c6ca5929a611cbd69204907a83995
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 760229
  timestamp: 1685695754230
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dav1d-1.2.1-h31becfc_0.conda
  sha256: 33fe66d025cf5bac7745196d1a3dd7a437abcf2dbce66043e9745218169f7e17
  md5: 6e5a87182d66b2d1328a96b61ca43a62
  depends:
  - libgcc-ng >=12
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 347363
  timestamp: 1685696690003
- conda: https://conda.anaconda.org/conda-forge/linux-64/dbus-1.16.2-h3c4dab8_0.conda
  sha256: 3b988146a50e165f0fa4e839545c679af88e4782ec284cc7b6d07dd226d6a068
  md5: 679616eb5ad4e521c83da4650860aba7
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - libglib >=2.84.2,<3.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 437860
  timestamp: 1747855126005
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/dbus-1.16.2-heda779d_0.conda
  sha256: 5c9166bbbe1ea7d0685a1549aad4ea887b1eb3a07e752389f86b185ef8eac99a
  md5: 9203b74bb1f3fa0d6f308094b3b44c1e
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  - libexpat >=2.7.0,<3.0a0
  - libglib >=2.84.2,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 469781
  timestamp: 1747855172617
- pypi: https://files.pythonhosted.org/packages/91/a1/cf2472db20f7ce4a6be1253a81cfdf85ad9c7885ffbed7047fb72c24cf87/distlib-0.3.9-py2.py3-none-any.whl
  name: distlib
  version: 0.3.9
  sha256: 47f8c22fd27c27e25a65601af709b38e4f0a45ea4fc2e710f65755fa8caaaf87
- pypi: https://files.pythonhosted.org/packages/53/50/b1222562c6d270fea83e9c9075b8e8600b8479150a18e4516a6138b980d1/fastapi-0.115.14-py3-none-any.whl
  name: fastapi
  version: 0.115.14
  sha256: 6c0c8bf9420bd58f565e585036d971872472b4f7d3f6c73b698e10cffdefb3ca
  requires_dist:
  - starlette>=0.40.0,<0.47.0
  - pydantic>=1.7.4,!=1.8,!=1.8.1,!=2.0.0,!=2.0.1,!=2.1.0,<3.0.0
  - typing-extensions>=4.8.0
  - fastapi-cli[standard]>=0.0.5 ; extra == 'standard'
  - httpx>=0.23.0 ; extra == 'standard'
  - jinja2>=3.1.5 ; extra == 'standard'
  - python-multipart>=0.0.18 ; extra == 'standard'
  - email-validator>=2.0.0 ; extra == 'standard'
  - uvicorn[standard]>=0.12.0 ; extra == 'standard'
  - fastapi-cli[standard]>=0.0.5 ; extra == 'all'
  - httpx>=0.23.0 ; extra == 'all'
  - jinja2>=3.1.5 ; extra == 'all'
  - python-multipart>=0.0.18 ; extra == 'all'
  - itsdangerous>=1.1.0 ; extra == 'all'
  - pyyaml>=5.3.1 ; extra == 'all'
  - ujson>=4.0.1,!=4.0.2,!=4.1.0,!=4.2.0,!=4.3.0,!=5.0.0,!=5.1.0 ; extra == 'all'
  - orjson>=3.2.1 ; extra == 'all'
  - email-validator>=2.0.0 ; extra == 'all'
  - uvicorn[standard]>=0.12.0 ; extra == 'all'
  - pydantic-settings>=2.0.0 ; extra == 'all'
  - pydantic-extra-types>=2.0.0 ; extra == 'all'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ffmpeg-7.1.1-gpl_h127656b_906.conda
  sha256: e8e93a1afd93bed11ccf2a2224d2b92b2af8758c89576ed87ff4df7f3269604f
  md5: 28cffcba871461840275632bc4653ce3
  depends:
  - __glibc >=2.17,<3.0.a0
  - alsa-lib >=1.2.14,<1.3.0a0
  - aom >=3.9.1,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - gmp >=6.3.0,<7.0a0
  - harfbuzz >=11.0.1
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.3,<0.17.4.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libopenvino >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-batch-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-hetero-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-cpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-gpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-intel-npu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-ir-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-onnx-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-paddle-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-pytorch-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2025.0.0,<2025.0.1.0a0
  - libopus >=1.5.2,<2.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libva >=2.22.0,<3.0a0
  - libvorbis >=1.3.7,<1.4.0a0
  - libvpx >=1.14.1,<1.15.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openh264 >=2.6.0,<2.6.1.0a0
  - openssl >=3.5.0,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - sdl2 >=2.32.54,<3.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  constrains:
  - __cuda  >=12.8
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 10377191
  timestamp: 1748704974937
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ffmpeg-7.1.1-gpl_h30b7fc1_906.conda
  sha256: 157dbd7fdd226448343f962c7fcb4c5b5c2fa12dd1e0f1f88f4c16559522c02d
  md5: 74fb3d97aeebfb19ed743f2a2f2e9ec3
  depends:
  - alsa-lib >=1.2.14,<1.3.0a0
  - aom >=3.9.1,<3.10.0a0
  - bzip2 >=1.0.8,<2.0a0
  - dav1d >=1.2.1,<1.2.2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - gmp >=6.3.0,<7.0a0
  - harfbuzz >=11.0.1
  - lame >=3.100,<3.101.0a0
  - libass >=0.17.3,<0.17.4.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libopenvino >=2025.0.0,<2025.0.1.0a0
  - libopenvino-arm-cpu-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-batch-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-auto-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-hetero-plugin >=2025.0.0,<2025.0.1.0a0
  - libopenvino-ir-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-onnx-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-paddle-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-pytorch-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-frontend >=2025.0.0,<2025.0.1.0a0
  - libopenvino-tensorflow-lite-frontend >=2025.0.0,<2025.0.1.0a0
  - libopus >=1.5.2,<2.0a0
  - librsvg >=2.58.4,<3.0a0
  - libstdcxx >=13
  - libvorbis >=1.3.7,<1.4.0a0
  - libvpx >=1.14.1,<1.15.0a0
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - libzlib >=1.3.1,<2.0a0
  - openh264 >=2.6.0,<2.6.1.0a0
  - openssl >=3.5.0,<4.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - sdl2 >=2.32.54,<3.0a0
  - svt-av1 >=3.0.2,<3.0.3.0a0
  - x264 >=1!164.3095,<1!165
  - x265 >=3.5,<3.6.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  constrains:
  - __cuda  >=12.8
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 10028157
  timestamp: 1748705029112
- pypi: https://files.pythonhosted.org/packages/4d/36/2a115987e2d8c300a974597416d9de88f2444426de9571f4b59b2cca3acc/filelock-3.18.0-py3-none-any.whl
  name: filelock
  version: 3.18.0
  sha256: c401f4f8377c4464e6db25fff06205fd89bdd83b65eb0488ed1b160f780e21de
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'testing'
  - coverage>=7.6.10 ; extra == 'testing'
  - diff-cover>=9.2.1 ; extra == 'testing'
  - pytest-asyncio>=0.25.2 ; extra == 'testing'
  - pytest-cov>=6 ; extra == 'testing'
  - pytest-mock>=3.14 ; extra == 'testing'
  - pytest-timeout>=2.3.1 ; extra == 'testing'
  - pytest>=8.3.4 ; extra == 'testing'
  - virtualenv>=20.28.1 ; extra == 'testing'
  - typing-extensions>=4.12.2 ; python_full_version < '3.11' and extra == 'typing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/9f/56/13ab06b4f93ca7cac71078fbe37fcea175d3216f31f85c3168a6bbd0bb9a/flake8-7.3.0-py2.py3-none-any.whl
  name: flake8
  version: 7.3.0
  sha256: b9696257b9ce8beb888cdbe31cf885c90d31928fe202be0889a7cdafad32f01e
  requires_dist:
  - mccabe>=0.7.0,<0.8.0
  - pycodestyle>=2.14.0,<2.15.0
  - pyflakes>=3.4.0,<3.5.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-dejavu-sans-mono-2.37-hab24e00_0.tar.bz2
  sha256: 58d7f40d2940dd0a8aa28651239adbf5613254df0f75789919c4e6762054403b
  md5: 0c96522c6bdaed4b1566d11387caaf45
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 397370
  timestamp: 1566932522327
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-inconsolata-3.000-h77eed37_0.tar.bz2
  sha256: c52a29fdac682c20d252facc50f01e7c2e7ceac52aa9817aaf0bb83f7559ec5c
  md5: 34893075a5c9e55cdafac56607368fc6
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 96530
  timestamp: 1620479909603
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-source-code-pro-2.038-h77eed37_0.tar.bz2
  sha256: 00925c8c055a2275614b4d983e1df637245e19058d79fc7dd1a93b8d9fb4b139
  md5: 4d59c254e01d9cde7957100457e2d5fb
  license: OFL-1.1
  license_family: Other
  purls: []
  size: 700814
  timestamp: 1620479612257
- conda: https://conda.anaconda.org/conda-forge/noarch/font-ttf-ubuntu-0.83-h77eed37_3.conda
  sha256: 2821ec1dc454bd8b9a31d0ed22a7ce22422c0aef163c59f49dfdf915d0f0ca14
  md5: 49023d73832ef61042f6a237cb2687e7
  license: LicenseRef-Ubuntu-Font-Licence-Version-1.0
  license_family: Other
  purls: []
  size: 1620504
  timestamp: 1727511233259
- conda: https://conda.anaconda.org/conda-forge/linux-64/fontconfig-2.15.0-h7e30c49_1.conda
  sha256: 7093aa19d6df5ccb6ca50329ef8510c6acb6b0d8001191909397368b65b02113
  md5: 8f5b0b297b59e1ac160ad4beec99dbee
  depends:
  - __glibc >=2.17,<3.0.a0
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 265599
  timestamp: 1730283881107
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fontconfig-2.15.0-h8dda3cd_1.conda
  sha256: fe023bb8917c8a3138af86ef537b70c8c5d60c44f93946a87d1e8bb1a6634b55
  md5: 112b71b6af28b47c624bcbeefeea685b
  depends:
  - freetype >=2.12.1,<3.0a0
  - libexpat >=2.6.3,<3.0a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 277832
  timestamp: 1730284967179
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-ecosystem-1-0.tar.bz2
  sha256: a997f2f1921bb9c9d76e6fa2f6b408b7fa549edd349a77639c9fe7a23ea93e61
  md5: fee5683a3f04bd15cbd8318b096a27ab
  depends:
  - fonts-conda-forge
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3667
  timestamp: 1566974674465
- conda: https://conda.anaconda.org/conda-forge/noarch/fonts-conda-forge-1-0.tar.bz2
  sha256: 53f23a3319466053818540bcdf2091f253cbdbab1e0e9ae7b9e509dcaa2a5e38
  md5: f766549260d6815b0c52253f1fb1bb29
  depends:
  - font-ttf-dejavu-sans-mono
  - font-ttf-inconsolata
  - font-ttf-source-code-pro
  - font-ttf-ubuntu
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 4102
  timestamp: 1566932280397
- conda: https://conda.anaconda.org/conda-forge/linux-64/freetype-2.13.3-ha770c72_1.conda
  sha256: 7ef7d477c43c12a5b4cddcf048a83277414512d1116aba62ebadfa7056a7d84f
  md5: 9ccd736d31e0c6e41f54e704e5312811
  depends:
  - libfreetype 2.13.3 ha770c72_1
  - libfreetype6 2.13.3 h48d6fc4_1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 172450
  timestamp: 1745369996765
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/freetype-2.13.3-h8af1aa0_1.conda
  sha256: 3b3ff45ac1fc880fbc8268477d29901a8fead32fb2241f98e4f2a1acffe6eea2
  md5: 71c4cbe1b384a8e7b56993394a435343
  depends:
  - libfreetype 2.13.3 h8af1aa0_1
  - libfreetype6 2.13.3 he93130f_1
  license: GPL-2.0-only OR FTL
  purls: []
  size: 172259
  timestamp: 1745370055170
- conda: https://conda.anaconda.org/conda-forge/linux-64/fribidi-1.0.10-h36c2ea0_0.tar.bz2
  sha256: 5d7b6c0ee7743ba41399e9e05a58ccc1cfc903942e49ff6f677f6e423ea7a627
  md5: ac7bc6a654f8f41b352b38f4051135f8
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  purls: []
  size: 114383
  timestamp: 1604416621168
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/fribidi-1.0.10-hb9de7d4_0.tar.bz2
  sha256: bcb5a40f1aaf4ea8cda2fc6b2b12aa336403772121350281ce31fd2d9d3e214e
  md5: f6c91a43eace6fb926a8730b3b9a8a50
  depends:
  - libgcc-ng >=7.5.0
  license: LGPL-2.1
  purls: []
  size: 115689
  timestamp: 1604417149643
- pypi: https://files.pythonhosted.org/packages/19/7c/71bb0bbe0832793c601fff68cd0cf6143753d0c667f9aec93d3c323f4b55/frozenlist-1.7.0-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: frozenlist
  version: 1.7.0
  sha256: dab46c723eeb2c255a64f9dc05b8dd601fde66d6b19cdb82b2e09cc6ff8d8b5d
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/72/31/bc8c5c99c7818293458fe745dab4fd5730ff49697ccc82b554eb69f16a24/frozenlist-1.7.0-cp313-cp313-manylinux_2_5_x86_64.manylinux1_x86_64.manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: frozenlist
  version: 1.7.0
  sha256: 8bd7eb96a675f18aa5c553eb7ddc24a43c8c18f22e1f9925528128c052cdbe00
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/gdk-pixbuf-2.42.12-hb9ae30d_0.conda
  sha256: d5283b95a8d49dcd88d29b360d8b38694aaa905d968d156d72ab71d32b38facb
  md5: 201db6c2d9a3c5e46573ac4cb2e92f4f
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 528149
  timestamp: 1715782983957
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gdk-pixbuf-2.42.12-ha61d561_0.conda
  sha256: 608f64aa9cf3085e91da8d417aa7680715130b4da73d8aabc50b19e29de697d2
  md5: 332ed304e6d1c1333ccbdc0fdd722fe9
  depends:
  - libgcc-ng >=12
  - libglib >=2.80.2,<3.0a0
  - libjpeg-turbo >=3.0.0,<4.0a0
  - libpng >=1.6.43,<1.7.0a0
  - libtiff >=4.6.0,<4.8.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 536613
  timestamp: 1715784386033
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-0.24.1-h5888daf_0.conda
  sha256: 88db27c666e1f8515174bf622a3e2ad983c94d69e3a23925089e476b9b06ad00
  md5: c63e7590d4d6f4c85721040ed8b12888
  depends:
  - __glibc >=2.17,<3.0.a0
  - gettext-tools 0.24.1 h5888daf_0
  - libasprintf 0.24.1 h8e693c7_0
  - libasprintf-devel 0.24.1 h8e693c7_0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  - libgettextpo-devel 0.24.1 h5888daf_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  purls: []
  size: 511988
  timestamp: 1746228987123
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-0.25.1-h5ad3122_0.conda
  sha256: 510e7eba15e6ba71cd5a2ae403128d56b3bb990878c8110f3abc652f823b4af8
  md5: 1e99d353785a5302bce1a5a86d249b2b
  depends:
  - gettext-tools 0.25.1 h5ad3122_0
  - libasprintf 0.25.1 h5e0f5ae_0
  - libasprintf-devel 0.25.1 h5e0f5ae_0
  - libgcc >=13
  - libgettextpo 0.25.1 h5ad3122_0
  - libgettextpo-devel 0.25.1 h5ad3122_0
  - libstdcxx >=13
  license: LGPL-2.1-or-later AND GPL-3.0-or-later
  purls: []
  size: 534760
  timestamp: 1751557634743
- conda: https://conda.anaconda.org/conda-forge/linux-64/gettext-tools-0.24.1-h5888daf_0.conda
  sha256: 3ba33868630b903e3cda7a9176363cdf02710fb8f961efed5f8200c4d53fb4e3
  md5: d54305672f0361c2f3886750e7165b5f
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 3129801
  timestamp: 1746228937647
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gettext-tools-0.25.1-h5ad3122_0.conda
  sha256: 7b03cc531c9c2d567eb81dffe9f5688c83fbcdfa4882eec3a2045ec43218806f
  md5: 4215d91c0eaae5274a36a3f211898c91
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 3999301
  timestamp: 1751557600737
- conda: https://conda.anaconda.org/conda-forge/linux-64/gmp-6.3.0-hac33072_2.conda
  sha256: 309cf4f04fec0c31b6771a5809a1909b4b3154a2208f52351e1ada006f4c750c
  md5: c94a5994ef49749880a8139cf9afcbe1
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 460055
  timestamp: 1718980856608
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/gmp-6.3.0-h0a1ffab_2.conda
  sha256: a5e341cbf797c65d2477b27d99091393edbaa5178c7d69b7463bb105b0488e69
  md5: 7cbfb3a8bb1b78a7f5518654ac6725ad
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: GPL-2.0-or-later OR LGPL-3.0-or-later
  purls: []
  size: 417323
  timestamp: 1718980707330
- pypi: https://files.pythonhosted.org/packages/14/4b/ead00905132820b623732b175d66354e9d3e69fcf2a5dcdab780664e7896/google_api_core-2.25.1-py3-none-any.whl
  name: google-api-core
  version: 2.25.1
  sha256: 8a2a56c1fef82987a524371f99f3bd0143702fecc670c72e600c1cda6bf8dbb7
  requires_dist:
  - googleapis-common-protos>=1.56.2,<2.0.0
  - protobuf>=3.19.5,!=3.20.0,!=3.20.1,!=4.21.0,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0
  - proto-plus>=1.22.3,<2.0.0
  - proto-plus>=1.25.0,<2.0.0 ; python_full_version >= '3.13'
  - google-auth>=2.14.1,<3.0.0
  - requests>=2.18.0,<3.0.0
  - google-auth[aiohttp]>=2.35.0,<3.0.0 ; extra == 'async-rest'
  - grpcio>=1.33.2,<2.0.0 ; extra == 'grpc'
  - grpcio>=1.49.1,<2.0.0 ; python_full_version >= '3.11' and extra == 'grpc'
  - grpcio-status>=1.33.2,<2.0.0 ; extra == 'grpc'
  - grpcio-status>=1.49.1,<2.0.0 ; python_full_version >= '3.11' and extra == 'grpc'
  - grpcio-gcp>=0.2.2,<1.0.0 ; extra == 'grpcgcp'
  - grpcio-gcp>=0.2.2,<1.0.0 ; extra == 'grpcio-gcp'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/f8/b8/2800f1aee399ea325fa675244de5178ab938485506ded2d17acf73e23cdb/google_api_python_client-2.175.0-py3-none-any.whl
  name: google-api-python-client
  version: 2.175.0
  sha256: 5f4313a914d11d2b0840d1daa336caef3f53e28e8234077c139f7b236fba9622
  requires_dist:
  - httplib2>=0.19.0,<1.0.0
  - google-auth>=1.32.0,!=2.24.0,!=2.25.0,<3.0.0
  - google-auth-httplib2>=0.2.0,<1.0.0
  - google-api-core>=1.31.5,!=2.0.*,!=2.1.*,!=2.2.*,!=2.3.0,<3.0.0
  - uritemplate>=3.0.1,<5
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/17/63/b19553b658a1692443c62bd07e5868adaa0ad746a0751ba62c59568cd45b/google_auth-2.40.3-py2.py3-none-any.whl
  name: google-auth
  version: 2.40.3
  sha256: 1370d4593e86213563547f97a92752fc658456fe4514c809544f330fed45a7ca
  requires_dist:
  - cachetools>=2.0.0,<6.0
  - pyasn1-modules>=0.2.1
  - rsa>=3.1.4,<5
  - aiohttp>=3.6.2,<4.0.0 ; extra == 'aiohttp'
  - requests>=2.20.0,<3.0.0 ; extra == 'aiohttp'
  - cryptography ; extra == 'enterprise-cert'
  - pyopenssl ; extra == 'enterprise-cert'
  - pyjwt>=2.0 ; extra == 'pyjwt'
  - cryptography>=38.0.3 ; extra == 'pyjwt'
  - cryptography<39.0.0 ; python_full_version < '3.8' and extra == 'pyjwt'
  - pyopenssl>=20.0.0 ; extra == 'pyopenssl'
  - cryptography>=38.0.3 ; extra == 'pyopenssl'
  - cryptography<39.0.0 ; python_full_version < '3.8' and extra == 'pyopenssl'
  - pyu2f>=0.1.5 ; extra == 'reauth'
  - requests>=2.20.0,<3.0.0 ; extra == 'requests'
  - grpcio ; extra == 'testing'
  - flask ; extra == 'testing'
  - freezegun ; extra == 'testing'
  - mock ; extra == 'testing'
  - oauth2client ; extra == 'testing'
  - pyjwt>=2.0 ; extra == 'testing'
  - cryptography>=38.0.3 ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-localserver ; extra == 'testing'
  - pyopenssl>=20.0.0 ; extra == 'testing'
  - pyu2f>=0.1.5 ; extra == 'testing'
  - responses ; extra == 'testing'
  - urllib3 ; extra == 'testing'
  - packaging ; extra == 'testing'
  - aiohttp>=3.6.2,<4.0.0 ; extra == 'testing'
  - requests>=2.20.0,<3.0.0 ; extra == 'testing'
  - aioresponses ; extra == 'testing'
  - pytest-asyncio ; extra == 'testing'
  - pyopenssl<24.3.0 ; extra == 'testing'
  - aiohttp<3.10.0 ; extra == 'testing'
  - cryptography<39.0.0 ; python_full_version < '3.8' and extra == 'testing'
  - urllib3 ; extra == 'urllib3'
  - packaging ; extra == 'urllib3'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/be/8a/fe34d2f3f9470a27b01c9e76226965863f153d5fbe276f83608562e49c04/google_auth_httplib2-0.2.0-py2.py3-none-any.whl
  name: google-auth-httplib2
  version: 0.2.0
  sha256: b65a0a2123300dd71281a7bf6e64d65a0759287df52729bdd1ae2e47dc311a3d
  requires_dist:
  - google-auth
  - httplib2>=0.19.0
- pypi: https://files.pythonhosted.org/packages/ac/84/40ee070be95771acd2f4418981edb834979424565c3eec3cd88b6aa09d24/google_auth_oauthlib-1.2.2-py3-none-any.whl
  name: google-auth-oauthlib
  version: 1.2.2
  sha256: fd619506f4b3908b5df17b65f39ca8d66ea56986e5472eb5978fd8f3786f00a2
  requires_dist:
  - google-auth>=2.15.0
  - requests-oauthlib>=0.7.0
  - click>=6.0.0 ; extra == 'tool'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/86/f1/62a193f0227cf15a920390abe675f386dec35f7ae3ffe6da582d3ade42c7/googleapis_common_protos-1.70.0-py3-none-any.whl
  name: googleapis-common-protos
  version: 1.70.0
  sha256: b8bfcca8c25a2bb253e0e0b0adaf8c00773e5e6af6fd92397576680b807e0fd8
  requires_dist:
  - protobuf>=3.20.2,!=4.21.1,!=4.21.2,!=4.21.3,!=4.21.4,!=4.21.5,<7.0.0
  - grpcio>=1.44.0,<2.0.0 ; extra == 'grpc'
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/graphite2-1.3.14-h5888daf_0.conda
  sha256: cac69f3ff7756912bbed4c28363de94f545856b35033c0b86193366b95f5317d
  md5: 951ff8d9e5536896408e89d63230b8d5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 98419
  timestamp: 1750079957535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/graphite2-1.3.14-h5ad3122_0.conda
  sha256: 957d9bcf7f8b2d8925a9af238189b372ba42c0fdbda4248cd8bd76684781af3d
  md5: 087ecf989fc23fc50944a06fddf5f3bc
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.0-or-later
  license_family: LGPL
  purls: []
  size: 101397
  timestamp: 1750080039341
- pypi: https://files.pythonhosted.org/packages/26/f2/ad51331a157c7015c675702e2d5230c243695c788f8f75feba1af32b3617/greenlet-3.2.3-cp313-cp313-manylinux_2_24_x86_64.manylinux_2_28_x86_64.whl
  name: greenlet
  version: 3.2.3
  sha256: 2c724620a101f8170065d7dded3f962a2aea7a7dae133a009cada42847e04a7b
  requires_dist:
  - sphinx ; extra == 'docs'
  - furo ; extra == 'docs'
  - objgraph ; extra == 'test'
  - psutil ; extra == 'test'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/48/ae/91a957ba60482d3fecf9be49bc3948f341d706b52ddb9d83a70d42abd498/greenlet-3.2.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.whl
  name: greenlet
  version: 3.2.3
  sha256: a07d3472c2a93117af3b0136f246b2833fdc0b542d4a9799ae5f41c28323faef
  requires_dist:
  - sphinx ; extra == 'docs'
  - furo ; extra == 'docs'
  - objgraph ; extra == 'test'
  - psutil ; extra == 'test'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/04/4b/29cac41a4d98d144bf5f6d33995617b185d14b22401f75ca86f384e87ff1/h11-0.16.0-py3-none-any.whl
  name: h11
  version: 0.16.0
  sha256: 63cf8bbe7522de3bf65932fda1d9c2772064ffb3dae62d55932da54b31cb6c86
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/harfbuzz-11.2.1-h3beb420_0.conda
  sha256: 5bd0f3674808862838d6e2efc0b3075e561c34309c5c2f4c976f7f1f57c91112
  md5: 0e6e192d4b3d95708ad192d957cf3163
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1730226
  timestamp: 1747091044218
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/harfbuzz-11.2.1-h405b6a2_0.conda
  sha256: 2f7754c197fc1b7e57cf5b4063298834818889561c0c462b7fe363742defdbd5
  md5: b55680fc90e9747dc858e7ceb0abc2b2
  depends:
  - cairo >=1.18.4,<2.0a0
  - freetype
  - graphite2
  - icu >=75.1,<76.0a0
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.1,<3.0a0
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 1746366
  timestamp: 1747094097917
- pypi: https://files.pythonhosted.org/packages/7e/f5/f66802a942d491edb555dd61e3a9961140fd64c90bce1eafd741609d334d/httpcore-1.0.9-py3-none-any.whl
  name: httpcore
  version: 1.0.9
  sha256: 2d400746a40668fc9dec9810239072b40b4484b640a8c38fd654a024c7a1bf55
  requires_dist:
  - certifi
  - h11>=0.16
  - anyio>=4.0,<5.0 ; extra == 'asyncio'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - trio>=0.22.0,<1.0 ; extra == 'trio'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/a8/6c/d2fbdaaa5959339d53ba38e94c123e4e84b8fbc4b84beb0e70d7c1608486/httplib2-0.22.0-py3-none-any.whl
  name: httplib2
  version: 0.22.0
  sha256: 14ae0a53c1ba8f3d37e9e27cf37eabb0fb9980f435ba405d546948b009dd64dc
  requires_dist:
  - pyparsing>=2.4.2,<3 ; python_full_version < '3.0'
  - pyparsing>=2.4.2,!=3.0.0,!=3.0.1,!=3.0.2,!=3.0.3,<4 ; python_full_version >= '3.1'
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*'
- pypi: https://files.pythonhosted.org/packages/2a/39/e50c7c3a983047577ee07d2a9e53faf5a69493943ec3f6a384bdc792deb2/httpx-0.28.1-py3-none-any.whl
  name: httpx
  version: 0.28.1
  sha256: d909fcccc110f8c7faf814ca82a9a4d816bc5a6dbfea25d6591d6985b8ba59ad
  requires_dist:
  - anyio
  - certifi
  - httpcore==1.*
  - idna
  - brotli ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - click==8.* ; extra == 'cli'
  - pygments==2.* ; extra == 'cli'
  - rich>=10,<14 ; extra == 'cli'
  - h2>=3,<5 ; extra == 'http2'
  - socksio==1.* ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/icu-75.1-he02047a_0.conda
  sha256: 71e750d509f5fa3421087ba88ef9a7b9be11c53174af3aa4d06aff4c18b38e8e
  md5: 8b189310083baabfb622af68fd9d3ae3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12129203
  timestamp: 1720853576813
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/icu-75.1-hf9b3779_0.conda
  sha256: 813298f2e54ef087dbfc9cc2e56e08ded41de65cff34c639cc8ba4e27e4540c9
  md5: 268203e8b983fddb6412b36f2024e75c
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: MIT
  license_family: MIT
  purls: []
  size: 12282786
  timestamp: 1720853454991
- pypi: https://files.pythonhosted.org/packages/7a/cd/18f8da995b658420625f7ef13f037be53ae04ec5ad33f9b718240dcfd48c/identify-2.6.12-py2.py3-none-any.whl
  name: identify
  version: 2.6.12
  sha256: ad9672d5a72e0d2ff7c5c8809b62dfa60458626352fb0eb7b55e69bdc45334a2
  requires_dist:
  - ukkonen ; extra == 'license'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/76/c6/c88e154df9c4e1a2a66ccf0005a88dfb2650c1dffb6f5ce603dfbd452ce3/idna-3.10-py3-none-any.whl
  name: idna
  version: '3.10'
  sha256: 946d195a0d259cbba61165e88e65941f16e9b36ea6ddb97f00452bae8b1287d3
  requires_dist:
  - ruff>=0.6.2 ; extra == 'all'
  - mypy>=1.11.2 ; extra == 'all'
  - pytest>=8.3.2 ; extra == 'all'
  - flake8>=7.1.1 ; extra == 'all'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/2c/e1/e6716421ea10d38022b952c159d5161ca1193197fb744506875fbb87ea7b/iniconfig-2.1.0-py3-none-any.whl
  name: iniconfig
  version: 2.1.0
  sha256: 9deba5723312380e77435581c6bf4935c94cbfab9b1ed33ef8d238ea168eb760
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/lame-3.100-h166bdaf_1003.tar.bz2
  sha256: aad2a703b9d7b038c0f745b853c6bb5f122988fe1a7a096e0e606d9cbec4eaab
  md5: a8832b479f93521a9e7b5b743803be51
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  purls: []
  size: 508258
  timestamp: 1664996250081
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lame-3.100-h4e544f5_1003.tar.bz2
  sha256: 2502904a42df6d94bd743f7b73915415391dd6d31d5f50cb57c0a54a108e7b0a
  md5: ab05bcf82d8509b4243f07e93bada144
  depends:
  - libgcc-ng >=12
  license: LGPL-2.0-only
  license_family: LGPL
  purls: []
  size: 604863
  timestamp: 1664997611416
- conda: https://conda.anaconda.org/conda-forge/linux-64/ld_impl_linux-64-2.43-h1423503_5.conda
  sha256: dcd2b1a065bbf5c54004ddf6551c775a8eb6993c8298ca8a6b92041ed413f785
  md5: 6dc9e1305e7d3129af4ad0dabda30e56
  depends:
  - __glibc >=2.17,<3.0.a0
  constrains:
  - binutils_impl_linux-64 2.43
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 670635
  timestamp: 1749858327854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ld_impl_linux-aarch64-2.44-h5e2c951_0.conda
  sha256: 878637939420ff1d1779306830d637f72d787824cbdf0301c20e00e1893fa400
  md5: 9a1c1446a3ae12fa5e58ef6e165413ef
  constrains:
  - binutils_impl_linux-aarch64 2.44
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 708304
  timestamp: 1751601812725
- conda: https://conda.anaconda.org/conda-forge/linux-64/lerc-4.0.0-h0aef613_1.conda
  sha256: 412381a43d5ff9bbed82cd52a0bbca5b90623f62e41007c9c42d3870c60945ff
  md5: 9344155d33912347b37f0ae6c410a835
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 264243
  timestamp: 1745264221534
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lerc-4.0.0-hfdc4d58_1.conda
  sha256: f01df5bbf97783fac9b89be602b4d02f94353f5221acfd80c424ec1c9a8d276c
  md5: 60dceb7e876f4d74a9cbd42bbbc6b9cf
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 227184
  timestamp: 1745265544057
- conda: https://conda.anaconda.org/conda-forge/linux-64/level-zero-1.23.0-h84d6215_0.conda
  sha256: 143e01d63c103baf6cd27372736c90c75d78814d24105adcfb34900abd2bcdd5
  md5: 917379a89f84d15d3e871909553c2320
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 598789
  timestamp: 1750920203501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libabseil-20250127.1-cxx17_hbbce691_0.conda
  sha256: 65d5ca837c3ee67b9d769125c21dc857194d7f6181bb0e7bd98ae58597b457d0
  md5: 00290e549c5c8a32cc271020acc9ec6b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - abseil-cpp =20250127.1
  - libabseil-static =20250127.1=cxx17*
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1325007
  timestamp: 1742369558286
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libabseil-20250127.1-cxx17_h18dbdb1_0.conda
  sha256: 55b7f9d8faa4a0a08f9fc7bcbd7f4cdd3c232120bafa2e8f7e19014ea4aa1278
  md5: 71b972e18b2747a9d47bbbafc346b765
  depends:
  - libgcc >=13
  - libstdcxx >=13
  constrains:
  - libabseil-static =20250127.1=cxx17*
  - abseil-cpp =20250127.1
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 1348653
  timestamp: 1742369595937
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-0.24.1-h8e693c7_0.conda
  sha256: e30733a729eb6efd9cb316db0202897c882d46f6c20a0e647b4de8ec921b7218
  md5: 57566a81dd1e5aa3d98ac7582e8bfe03
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 53115
  timestamp: 1746228856865
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-0.25.1-h5e0f5ae_0.conda
  sha256: 146be90c237cf3d8399e44afe5f5d21ef9a15a7983ccea90e72d4ae0362f9b28
  md5: 1c5813f6be57f087b6659593248daf00
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 53434
  timestamp: 1751557548397
- conda: https://conda.anaconda.org/conda-forge/linux-64/libasprintf-devel-0.24.1-h8e693c7_0.conda
  sha256: ccbfc465456133042eea3e8d69bae009893f57a47a786f772c0af382bda7ad99
  md5: 8f66ed2e34507b7ae44afa31c3e4ec79
  depends:
  - __glibc >=2.17,<3.0.a0
  - libasprintf 0.24.1 h8e693c7_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 34680
  timestamp: 1746228884730
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libasprintf-devel-0.25.1-h5e0f5ae_0.conda
  sha256: cc2bb8ca349ba4dd4af7971a3dba006bc8643353acd9757b4d645a817ec0f899
  md5: 5df92d925fba917586f3ca31c96d8e6d
  depends:
  - libasprintf 0.25.1 h5e0f5ae_0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 34824
  timestamp: 1751557562978
- conda: https://conda.anaconda.org/conda-forge/linux-64/libass-0.17.3-h52826cd_2.conda
  sha256: 8a94e634de73be1e7548deaf6e3b992e0d30c628a24f23333af06ebb3a3e74cb
  md5: 01de25a48490709850221135890e09eb
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libzlib >=1.3.1,<2.0a0
  - libiconv >=1.18,<2.0a0
  - fribidi >=1.0.10,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - harfbuzz >=11.0.0,<12.0a0
  license: ISC
  purls: []
  size: 152563
  timestamp: 1743206970222
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libass-0.17.3-h3c9f632_2.conda
  sha256: 72551f77103bd9725cc57a1e6dff71059970ccc76c48c45240cdfd1987dfebd8
  md5: e7714c1e8fdaf41d5125dd73b28667bc
  depends:
  - libgcc >=13
  - freetype >=2.13.3,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libzlib >=1.3.1,<2.0a0
  - libiconv >=1.18,<2.0a0
  - fribidi >=1.0.10,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  license: ISC
  purls: []
  size: 173682
  timestamp: 1743206972213
- conda: https://conda.anaconda.org/conda-forge/linux-64/libcap-2.75-h39aace5_0.conda
  sha256: 9c84448305e7c9cc44ccec7757cf5afcb5a021f4579aa750a1fa6ea398783950
  md5: c44c16d6976d2aebbd65894d7741e67e
  depends:
  - __glibc >=2.17,<3.0.a0
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 120375
  timestamp: 1741176638215
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libcap-2.75-h51d75a7_0.conda
  sha256: d77e8bd8d5714a80c1fa88037e71d5c29f21bae1e9281528006c9c5a6175ac1a
  md5: c5456e13665779bf7a62dc7724ca2938
  depends:
  - attr >=2.5.1,<2.6.0a0
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 108212
  timestamp: 1741177682469
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdeflate-1.24-h86f0d12_0.conda
  sha256: 8420748ea1cc5f18ecc5068b4f24c7a023cc9b20971c99c824ba10641fb95ddf
  md5: 64f0c503da58ec25ebd359e4d990afa8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 72573
  timestamp: 1747040452262
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdeflate-1.24-he377734_0.conda
  sha256: dd0e4baa983803227ec50457731d6f41258b90b3530f579b5d3151d5a98af191
  md5: f0b3d6494663b3385bf87fc206d7451a
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 70417
  timestamp: 1747040440762
- conda: https://conda.anaconda.org/conda-forge/linux-64/libdrm-2.4.125-hb9d3cd8_0.conda
  sha256: f53458db897b93b4a81a6dbfd7915ed8fa4a54951f97c698dde6faa028aadfd2
  md5: 4c0ab57463117fbb8df85268415082f5
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 246161
  timestamp: 1749904704373
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libdrm-2.4.125-h86ecc28_0.conda
  sha256: 4413fda35527cf7a746c5e386fa5406349c0948d51fc20f7896732795a369e5d
  md5: c5e4a8dad08e393b3616651e963304e5
  depends:
  - libgcc >=13
  - libpciaccess >=0.18,<0.19.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 252778
  timestamp: 1749904786465
- conda: https://conda.anaconda.org/conda-forge/linux-64/libegl-1.7.0-ha4b6fd6_2.conda
  sha256: 7fd5408d359d05a969133e47af580183fbf38e2235b562193d427bb9dad79723
  md5: c151d5eb730e9b7480e6d48c0fc44048
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 44840
  timestamp: 1731330973553
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libegl-1.7.0-hd24410f_2.conda
  sha256: 8962abf38a58c235611ce356b9899f6caeb0352a8bce631b0bcc59352fda455e
  md5: cf105bce884e4ef8c8ccdca9fe6695e7
  depends:
  - libglvnd 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  purls: []
  size: 53551
  timestamp: 1731330990477
- conda: https://conda.anaconda.org/conda-forge/linux-64/libexpat-2.7.0-h5888daf_0.conda
  sha256: 33ab03438aee65d6aa667cf7d90c91e5e7d734c19a67aa4c7040742c0a13d505
  md5: db0bfbe7dd197b68ad5f30333bae6ce0
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 74427
  timestamp: 1743431794976
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libexpat-2.7.0-h5ad3122_0.conda
  sha256: e3a0d95fe787cccf286f5dced9fa9586465d3cd5ec8e04f7ad7f0e72c4afd089
  md5: d41a057e7968705dae8dcb7c8ba2c8dd
  depends:
  - libgcc >=13
  constrains:
  - expat 2.7.0.*
  license: MIT
  license_family: MIT
  purls: []
  size: 73155
  timestamp: 1743432002397
- conda: https://conda.anaconda.org/conda-forge/linux-64/libffi-3.4.6-h2dba641_1.conda
  sha256: 764432d32db45466e87f10621db5b74363a9f847d2b8b1f9743746cd160f06ab
  md5: ede4673863426c0883c0063d853bbd85
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 57433
  timestamp: 1743434498161
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libffi-3.4.6-he21f813_1.conda
  sha256: 608b8c8b0315423e524b48733d91edd43f95cb3354a765322ac306a858c2cd2e
  md5: 15a131f30cae36e9a655ca81fee9a285
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 55847
  timestamp: 1743434586764
- conda: https://conda.anaconda.org/conda-forge/linux-64/libflac-1.4.3-h59595ed_0.conda
  sha256: 65908b75fa7003167b8a8f0001e11e58ed5b1ef5e98b96ab2ba66d7c1b822c7d
  md5: ee48bf17cc83a00f59ca1494d5646869
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 394383
  timestamp: 1687765514062
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libflac-1.4.3-h2f0025b_0.conda
  sha256: b54935360349d3418b0663d787f20b3cba0b7ce3fcdf3ba5e7ef02b884759049
  md5: 520b12eab32a92e19b1f239ac545ec03
  depends:
  - gettext >=0.21.1,<1.0a0
  - libgcc-ng >=12
  - libogg 1.3.*
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 371550
  timestamp: 1687765491794
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype-2.13.3-ha770c72_1.conda
  sha256: 7be9b3dac469fe3c6146ff24398b685804dfc7a1de37607b84abd076f57cc115
  md5: 51f5be229d83ecd401fb369ab96ae669
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7693
  timestamp: 1745369988361
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype-2.13.3-h8af1aa0_1.conda
  sha256: c1bb6726b054b00ad509b9ace5e04f4bfe97e6fdaf5c4473c537e6c03d1f660b
  md5: 2d4a1c3dcabb80b4a56d5c34bdacea08
  depends:
  - libfreetype6 >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 7774
  timestamp: 1745370050680
- conda: https://conda.anaconda.org/conda-forge/linux-64/libfreetype6-2.13.3-h48d6fc4_1.conda
  sha256: 7759bd5c31efe5fbc36a7a1f8ca5244c2eabdbeb8fc1bee4b99cf989f35c7d81
  md5: 3c255be50a506c50765a93a6644f32fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 380134
  timestamp: 1745369987697
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libfreetype6-2.13.3-he93130f_1.conda
  sha256: 9f189f75bb79f6b97c48804e89b4f1db5dc3fba5729551e4cbd2deca98580635
  md5: 51eae9012d75b8f7e4b0adfe61a83330
  depends:
  - libgcc >=13
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  constrains:
  - freetype >=2.13.3
  license: GPL-2.0-only OR FTL
  purls: []
  size: 408198
  timestamp: 1745370049871
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-15.1.0-h767d61c_3.conda
  sha256: 59a87161212abe8acc57d318b0cc8636eb834cdfdfddcf1f588b5493644b39a3
  md5: 9e60c55e725c20d23125a5f0dd69af5d
  depends:
  - __glibc >=2.17,<3.0.a0
  - _openmp_mutex >=4.5
  constrains:
  - libgcc-ng ==15.1.0=*_3
  - libgomp 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 824921
  timestamp: 1750808216066
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-15.1.0-he277a41_3.conda
  sha256: a08e3f89d4cd7c5e18a7098419e378a8506ebfaf4dc1eaac59bf7b962ca66cdb
  md5: 409b902521be20c2efb69d2e0c5e3bc8
  depends:
  - _openmp_mutex >=4.5
  constrains:
  - libgomp 15.1.0 he277a41_3
  - libgcc-ng ==15.1.0=*_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 510464
  timestamp: 1750808926824
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcc-ng-15.1.0-h69a702a_3.conda
  sha256: b0b0a5ee6ce645a09578fc1cb70c180723346f8a45fdb6d23b3520591c6d6996
  md5: e66f2b8ad787e7beb0f846e4bd7e8493
  depends:
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 29033
  timestamp: 1750808224854
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcc-ng-15.1.0-he9431aa_3.conda
  sha256: 222eedd38467f7af8fb703c16cc1abf83038e7b6a09f707bbb4340e8ed589e14
  md5: 831062d3b6a4cdfdde1015be90016102
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29009
  timestamp: 1750808930406
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgcrypt-lib-1.11.1-hb9d3cd8_0.conda
  sha256: dc9c7d7a6c0e6639deee6fde2efdc7e119e7739a6b229fa5f9049a449bae6109
  md5: 8504a291085c9fb809b66cabd5834307
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 590353
  timestamp: 1747060639058
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgcrypt-lib-1.11.1-h86ecc28_0.conda
  sha256: 5c572886ae3bf8f55fbc8f18275317679b559a9dd00cf1f128d24057dc6de70e
  md5: 50df370cbbbcfb4aa67556879e6643a1
  depends:
  - libgcc >=13
  - libgpg-error >=1.55,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 652592
  timestamp: 1747060671875
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-0.24.1-h5888daf_0.conda
  sha256: 104f2341546e295d1136ab3010e81391bd3fd5be0f095db59266e8eba2082d37
  md5: 2ee6d71b72f75d50581f2f68e965efdb
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 171165
  timestamp: 1746228870846
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-0.25.1-h5ad3122_0.conda
  sha256: c8e5590166f4931a3ab01e444632f326e1bb00058c98078eb46b6e8968f1b1e9
  md5: ad7b109fbbff1407b1a7eeaa60d7086a
  depends:
  - libgcc >=13
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 225352
  timestamp: 1751557555903
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgettextpo-devel-0.24.1-h5888daf_0.conda
  sha256: a9a0cba030778eb2944a1f235dba51e503b66f8be0ce6f55f745173a515c3644
  md5: 8f04c7aae6a46503bc36d1ed5abc8c7c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libgettextpo 0.24.1 h5888daf_0
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 37234
  timestamp: 1746228897993
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgettextpo-devel-0.25.1-h5ad3122_0.conda
  sha256: a26e1982d062daba5bdd3a90a2ef77b323803d21d27cf4e941135f07037d6649
  md5: 0d9d56bac6e4249da2bede0588ae1c1b
  depends:
  - libgcc >=13
  - libgettextpo 0.25.1 h5ad3122_0
  license: GPL-3.0-or-later
  license_family: GPL
  purls: []
  size: 37460
  timestamp: 1751557569909
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgl-1.7.0-ha4b6fd6_2.conda
  sha256: dc2752241fa3d9e40ce552c1942d0a4b5eeb93740c9723873f6fcf8d39ef8d2d
  md5: 928b8be80851f5d8ffb016f9c81dae7a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - libglx 1.7.0 ha4b6fd6_2
  license: LicenseRef-libglvnd
  purls: []
  size: 134712
  timestamp: 1731330998354
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgl-1.7.0-hd24410f_2.conda
  sha256: 3e954380f16255d1c8ae5da3bd3044d3576a0e1ac2e3c3ff2fe8f2f1ad2e467a
  md5: 0d00176464ebb25af83d40736a2cd3bb
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - libglx 1.7.0 hd24410f_2
  license: LicenseRef-libglvnd
  purls: []
  size: 145442
  timestamp: 1731331005019
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglib-2.84.2-h3618099_0.conda
  sha256: a6b5cf4d443044bc9a0293dd12ca2015f0ebe5edfdc9c4abdde0b9947f9eb7bd
  md5: 072ab14a02164b7c0c089055368ff776
  depends:
  - __glibc >=2.17,<3.0.a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 3955066
  timestamp: 1747836671118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglib-2.84.2-hc022ef1_0.conda
  sha256: a74d52adc3b913e75185c0afaf9403c85f47c2c6ad585fdbd16f29b6c364a848
  md5: 51323eab8e9f049d001424828c4c25a4
  depends:
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - pcre2 >=10.45,<10.46.0a0
  constrains:
  - glib 2.84.2 *_0
  license: LGPL-2.1-or-later
  purls: []
  size: 4016850
  timestamp: 1747836804570
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglvnd-1.7.0-ha4b6fd6_2.conda
  sha256: 1175f8a7a0c68b7f81962699751bb6574e6f07db4c9f72825f978e3016f46850
  md5: 434ca7e50e40f4918ab701e3facd59a0
  depends:
  - __glibc >=2.17,<3.0.a0
  license: LicenseRef-libglvnd
  purls: []
  size: 132463
  timestamp: 1731330968309
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglvnd-1.7.0-hd24410f_2.conda
  sha256: 57ec3898a923d4bcc064669e90e8abfc4d1d945a13639470ba5f3748bd3090da
  md5: 9e115653741810778c9a915a2f8439e7
  license: LicenseRef-libglvnd
  purls: []
  size: 152135
  timestamp: 1731330986070
- conda: https://conda.anaconda.org/conda-forge/linux-64/libglx-1.7.0-ha4b6fd6_2.conda
  sha256: 2d35a679624a93ce5b3e9dd301fff92343db609b79f0363e6d0ceb3a6478bfa7
  md5: c8013e438185f33b13814c5c488acd5c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libglvnd 1.7.0 ha4b6fd6_2
  - xorg-libx11 >=1.8.10,<2.0a0
  license: LicenseRef-libglvnd
  purls: []
  size: 75504
  timestamp: 1731330988898
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libglx-1.7.0-hd24410f_2.conda
  sha256: 6591af640cb05a399fab47646025f8b1e1a06a0d4bbb4d2e320d6629b47a1c61
  md5: 1d4269e233636148696a67e2d30dad2a
  depends:
  - libglvnd 1.7.0 hd24410f_2
  - xorg-libx11 >=1.8.9,<2.0a0
  license: LicenseRef-libglvnd
  purls: []
  size: 77736
  timestamp: 1731330998960
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgomp-15.1.0-h767d61c_3.conda
  sha256: 43710ab4de0cd7ff8467abff8d11e7bb0e36569df04ce1c099d48601818f11d1
  md5: 3cd1a7238a0dd3d0860fdefc496cc854
  depends:
  - __glibc >=2.17,<3.0.a0
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 447068
  timestamp: 1750808138400
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgomp-15.1.0-he277a41_3.conda
  sha256: a6654342666271da9c396a41ea745dc6e56574806b42abb2be61511314f5cc40
  md5: b79b8a69669f9ac6311f9ff2e6bffdf2
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 449966
  timestamp: 1750808867863
- conda: https://conda.anaconda.org/conda-forge/linux-64/libgpg-error-1.55-h3f2d84a_0.conda
  sha256: 697334de4786a1067ea86853e520c64dd72b11a05137f5b318d8a444007b5e60
  md5: 2bd47db5807daade8500ed7ca4c512a4
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 312184
  timestamp: 1745575272035
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libgpg-error-1.55-h5ad3122_0.conda
  sha256: a744c0a137a084af7cee4a33de9bffb988182b5be4edb8a45d51d2a1efd3724c
  md5: 39f742598d0f18c8e1cb01712bc03ee8
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 327973
  timestamp: 1745575312848
- conda: https://conda.anaconda.org/conda-forge/linux-64/libhwloc-2.11.2-default_h0d58e46_1001.conda
  sha256: d14c016482e1409ae1c50109a9ff933460a50940d2682e745ab1c172b5282a69
  md5: 804ca9e91bcaea0824a341d55b1684f2
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2423200
  timestamp: 1731374922090
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libhwloc-2.11.2-default_h2c612a5_1001.conda
  sha256: 8c7bf410afb4f068063c718a8691de611eeb75f3d0c6122698c7961e90820445
  md5: 8f42119cdfd1ac905e19f0eeebe9ccfa
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libxml2 >=2.13.4,<2.14.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 2436762
  timestamp: 1731374851939
- conda: https://conda.anaconda.org/conda-forge/linux-64/libiconv-1.18-h4ce23a2_1.conda
  sha256: 18a4afe14f731bfb9cf388659994263904d20111e42f841e9eea1bb6f91f4ab4
  md5: e796ff8ddc598affdf7c173d6145f087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 713084
  timestamp: 1740128065462
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libiconv-1.18-hc99b53d_1.conda
  sha256: 3db14977036fe1f511a6dbecacbeff3fdb58482c5c0cf87a2ea3232f5a540836
  md5: 81541d85a45fbf4d0a29346176f1f21c
  depends:
  - libgcc >=13
  license: LGPL-2.1-only
  purls: []
  size: 718600
  timestamp: 1740130562607
- conda: https://conda.anaconda.org/conda-forge/linux-64/libjpeg-turbo-3.1.0-hb9d3cd8_0.conda
  sha256: 98b399287e27768bf79d48faba8a99a2289748c65cd342ca21033fab1860d4a4
  md5: 9fa334557db9f63da6c9285fd2a48638
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 628947
  timestamp: 1745268527144
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libjpeg-turbo-3.1.0-h86ecc28_0.conda
  sha256: c7e4f017eeadcabb30e2a95dae95aad27271d633835e55e5dae23c932ae7efab
  md5: a689388210d502364b79e8b19e7fa2cb
  depends:
  - libgcc >=13
  constrains:
  - jpeg <0.0.0a
  license: IJG AND BSD-3-Clause AND Zlib
  purls: []
  size: 653054
  timestamp: 1745268199701
- conda: https://conda.anaconda.org/conda-forge/linux-64/liblzma-5.8.1-hb9d3cd8_2.conda
  sha256: f2591c0069447bbe28d4d696b7fcb0c5bd0b4ac582769b89addbcf26fb3430d8
  md5: 1a580f7796c7bf6393fddb8bbbde58dc
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 112894
  timestamp: 1749230047870
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liblzma-5.8.1-h86ecc28_2.conda
  sha256: 498ea4b29155df69d7f20990a7028d75d91dbea24d04b2eb8a3d6ef328806849
  md5: 7d362346a479256857ab338588190da0
  depends:
  - libgcc >=13
  constrains:
  - xz 5.8.1.*
  license: 0BSD
  purls: []
  size: 125103
  timestamp: 1749232230009
- conda: https://conda.anaconda.org/conda-forge/linux-64/libmpdec-4.0.0-hb9d3cd8_0.conda
  sha256: 3aa92d4074d4063f2a162cd8ecb45dccac93e543e565c01a787e16a43501f7ee
  md5: c7e925f37e3b40d893459e625f6a53f1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 91183
  timestamp: 1748393666725
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libmpdec-4.0.0-h86ecc28_0.conda
  sha256: ef8697f934c80b347bf9d7ed45650928079e303bad01bd064995b0e3166d6e7a
  md5: 78cfed3f76d6f3f279736789d319af76
  depends:
  - libgcc >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 114064
  timestamp: 1748393729243
- conda: https://conda.anaconda.org/conda-forge/linux-64/libogg-1.3.5-hd0c01bc_1.conda
  sha256: ffb066ddf2e76953f92e06677021c73c85536098f1c21fcd15360dbc859e22e4
  md5: 68e52064ed3897463c0e958ab5c8f91b
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 218500
  timestamp: 1745825989535
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libogg-1.3.5-h86ecc28_1.conda
  sha256: 2c1b7c59badc2fd6c19b6926eabfce906c996068d38c2972bd1cfbe943c07420
  md5: 319df383ae401c40970ee4e9bc836c7a
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 220653
  timestamp: 1745826021156
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-2025.0.0-hdc3f47d_3.conda
  sha256: fe0e184141a3563d4c97134a1b7a60c66302cf0e2692d15d49c41382cdf61648
  md5: 3a88245058baa9d18ef4ea6df18ff63e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 5698665
  timestamp: 1742046924817
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-2025.0.0-hd63d6c0_3.conda
  sha256: d4e774708a073ba4a240fd2bc0f524d8b6d9fe68a24074bc7affe70c7fd9d8b7
  md5: 97277bfdfcc0dd59c0a74869fb31269a
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 5068959
  timestamp: 1742043279584
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-arm-cpu-plugin-2025.0.0-hd63d6c0_3.conda
  sha256: 1097bf9bfff8a9dade6b2a033b107aafed75d0dd2b4430a1754d8b89cb12f47d
  md5: 387c0cad41f9e9cf330da02e9f7d4898
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 8652305
  timestamp: 1742043300690
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-batch-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: b4c61b3e8fc4d7090a94e3fd3936faf347eea07cac993417153dd99bd293c08d
  md5: 2e349bafc75b212879bf70ef80e0d08c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 111823
  timestamp: 1742046947746
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-batch-plugin-2025.0.0-hf15766e_3.conda
  sha256: 829a98d1dd0859fec5536419c9d7b1b99a612a91c629f173f6e9f05003e85749
  md5: 70a507a1ce0a13f5562953631deec2fd
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 109653
  timestamp: 1742043331132
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-auto-plugin-2025.0.0-h4d9b6c2_3.conda
  sha256: ae72903e0718897b85aae2110d9bb1bfa9490b0496522e3735b65c771e7da0ea
  md5: 74d074a3ac7af3378e16bfa6ff9cba30
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 238973
  timestamp: 1742046961091
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-auto-plugin-2025.0.0-hf15766e_3.conda
  sha256: af207ffa6f3a8a150620ca32c2996e941645689596ad2dc923115cef3ac1706d
  md5: 8399dc85b397fdb3770613c4b10f5a49
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - tbb >=2021.13.0
  purls: []
  size: 227098
  timestamp: 1742043342711
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-hetero-plugin-2025.0.0-h981d57b_3.conda
  sha256: b2c9ef97907f9c77817290bfb898897b476cc7ccf1737f0b1254437dda3d4903
  md5: 21f7997d68220d7356c1f80dc500bfad
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 196083
  timestamp: 1742046974588
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-hetero-plugin-2025.0.0-ha8e9e04_3.conda
  sha256: 69c8e3a060a10900f6d35df32264f48560e153fe370c6a2ee7fcff1b969629bb
  md5: e12bff64bfd2ef9e282383afb3cccc13
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 187049
  timestamp: 1742043354710
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-cpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 9f6613906386a0c679c9a683ca97a5a2070111d9ada4f115c1806d921313e32d
  md5: 3385f38d15c7aebcc3b453e4d8dfb0fe
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 12419296
  timestamp: 1742046988488
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-gpu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 8430f87a3cc65d3ef1ec8f9bfa990f6fb635601ad34ce08d70209099ff03f39c
  md5: f2d50e234edd843d9d695f7da34c7e96
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - ocl-icd >=2.3.2,<3.0a0
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 10119530
  timestamp: 1742047030958
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-intel-npu-plugin-2025.0.0-hdc3f47d_3.conda
  sha256: 37ec3e304bf14d2d7b7781c4b6a8b3a54deae90bc7275f6ae160589ef219bcef
  md5: f632cad865436394eebd41c3afa2cda3
  depends:
  - __glibc >=2.17,<3.0.a0
  - level-zero >=1.21.2,<2.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  - tbb >=2021.13.0
  purls: []
  size: 1092544
  timestamp: 1742047065987
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-ir-frontend-2025.0.0-h981d57b_3.conda
  sha256: 268716b5c1858c1fddd51d63c7fcd7f3544ef04f221371ab6a2f9c579ca001e4
  md5: 94f25cc6fe70f507897abb8e61603023
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 206013
  timestamp: 1742047080381
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-ir-frontend-2025.0.0-ha8e9e04_3.conda
  sha256: 3901f6922cfbac4de21622445d8a201862f46f502c95251bd2cba11eb67bf839
  md5: a3edb4a113c03ec3a3db3f89c7dabfb8
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  - pugixml >=1.15,<1.16.0a0
  purls: []
  size: 197852
  timestamp: 1742043366449
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-onnx-frontend-2025.0.0-h0e684df_3.conda
  sha256: 5ce66c01f6ea365a497f488e8eecea8930b6a016f9809db7f33b8a1ebbe5644e
  md5: 7cd3272c3171c1d43ed1c2b3d6795269
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 1668681
  timestamp: 1742047094228
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-onnx-frontend-2025.0.0-hd8f0270_3.conda
  sha256: 72097ef28507f41ff371cb10540261b7cbd433a9932a9c42d073f4d56335bfbe
  md5: cf46d328c1b254d16d18128999d31d61
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 1466096
  timestamp: 1742043380485
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-paddle-frontend-2025.0.0-h0e684df_3.conda
  sha256: 826507ac4ea2d496bdbec02dd9e3c8ed2eab253daa9d7f9119a8bc05c516d026
  md5: 5b66cbc9965b429922b8e69cd4e464d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 690226
  timestamp: 1742047109935
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-paddle-frontend-2025.0.0-hd8f0270_3.conda
  sha256: b7666e63f7399e94599829b9b8901e1e6541d9d4d0c156359eb24846a434bcb7
  md5: 9d6043d6fae5342567173f949af80e4f
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  purls: []
  size: 625570
  timestamp: 1742043394408
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-pytorch-frontend-2025.0.0-h5888daf_3.conda
  sha256: fda07e70a23aac329be68ae488b790f548d687807f0e47bae7129df34f0adb5b
  md5: a6ece96eff7f60b2559ba699156b0edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  purls: []
  size: 1123885
  timestamp: 1742047125703
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-pytorch-frontend-2025.0.0-h5ad3122_3.conda
  sha256: 174f630bdc3ffc6728fc83aefef15cf9a9a9fcd00712ce809df7a3b5c37dae99
  md5: d740a43f206611d7ab09488a6cb2f8eb
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  purls: []
  size: 1016003
  timestamp: 1742043406713
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-frontend-2025.0.0-h684f15b_3.conda
  sha256: e02990fccd4676e362a026acff3d706b5839ebf6ae681d56a2903f62a63e03ef
  md5: e1aeb108f4731db088782c8a20abf40a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - snappy >=1.2.1,<1.3.0a0
  purls: []
  size: 1313789
  timestamp: 1742047140816
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-frontend-2025.0.0-h33e842c_3.conda
  sha256: 437fc934eaa6282258ac2dc3e58d276b208079ee2440264cd19b67a9b6ff6827
  md5: 9083c0e4a30698bdbab0598d964e4540
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.0,<20250128.0a0
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libprotobuf >=5.29.3,<5.29.4.0a0
  - libstdcxx >=13
  - snappy >=1.2.1,<1.3.0a0
  purls: []
  size: 1204132
  timestamp: 1742043420133
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5888daf_3.conda
  sha256: 236569eb4d472d75412a3384c2aad92b006afed721feec23ca08730a25932da7
  md5: a6fe9c25b834988ac88651aff731dd31
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libopenvino 2025.0.0 hdc3f47d_3
  - libstdcxx >=13
  purls: []
  size: 488142
  timestamp: 1742047155790
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopenvino-tensorflow-lite-frontend-2025.0.0-h5ad3122_3.conda
  sha256: e1328d5e6ef41e112c1e79d06e2309b89da302806a5ec7b18cf7bfe47d321be6
  md5: bb1da88624792f47b7ac93ae0bb8206e
  depends:
  - libgcc >=13
  - libopenvino 2025.0.0 hd63d6c0_3
  - libstdcxx >=13
  purls: []
  size: 445050
  timestamp: 1742043433188
- conda: https://conda.anaconda.org/conda-forge/linux-64/libopus-1.5.2-hd0c01bc_0.conda
  sha256: 786d43678d6d1dc5f88a6bad2d02830cfd5a0184e84a8caa45694049f0e3ea5f
  md5: b64523fb87ac6f87f0790f324ad43046
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 312472
  timestamp: 1744330953241
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libopus-1.5.2-h86ecc28_0.conda
  sha256: c887543068308fb0fd50175183a3513f60cd8eb1defc23adc3c89769fde80d48
  md5: 44b2cfec6e1b94723a960f8a5e6206ae
  depends:
  - libgcc >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 357115
  timestamp: 1744331282621
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpciaccess-0.18-hb9d3cd8_0.conda
  sha256: 0bd91de9b447a2991e666f284ae8c722ffb1d84acb594dbd0c031bd656fa32b2
  md5: 70e3400cbbfa03e96dcde7fc13e38c7b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 28424
  timestamp: 1749901812541
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpciaccess-0.18-h86ecc28_0.conda
  sha256: 7641dfdfe9bda7069ae94379e9924892f0b6604c1a016a3f76b230433bb280f2
  md5: 5044e160c5306968d956c2a0a2a440d6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 29512
  timestamp: 1749901899881
- conda: https://conda.anaconda.org/conda-forge/linux-64/libpng-1.6.49-h943b412_0.conda
  sha256: c8f5dc929ba5fcee525a66777498e03bbcbfefc05a0773e5163bb08ac5122f1a
  md5: 37511c874cf3b8d0034c8d24e73c0884
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 289506
  timestamp: 1750095629466
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libpng-1.6.50-hec79eb8_0.conda
  sha256: 7695dae2b6b59250784f99d395f230b5a880988297b5bfcdc3e311aa0ddde26e
  md5: 375b0e45424d5d77b8c572a5a1521b70
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: zlib-acknowledgement
  purls: []
  size: 297216
  timestamp: 1751561688084
- conda: https://conda.anaconda.org/conda-forge/linux-64/libprotobuf-5.29.3-h501fc15_1.conda
  sha256: 691af28446345674c6b3fb864d0e1a1574b6cc2f788e0f036d73a6b05dcf81cf
  md5: edb86556cf4a0c133e7932a1597ff236
  depends:
  - __glibc >=2.17,<3.0.a0
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3358788
  timestamp: 1745159546868
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libprotobuf-5.29.3-h4edc36e_1.conda
  sha256: 3dea67282f1e3442030ff9d4ee46747e5260dac3360db27f0e0227d913bbc744
  md5: 2f321e8f84944b3b41f7187bbc2bbedd
  depends:
  - libabseil * cxx17*
  - libabseil >=20250127.1,<20250128.0a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 3196210
  timestamp: 1745158850256
- conda: https://conda.anaconda.org/conda-forge/linux-64/librsvg-2.58.4-he92a37e_3.conda
  sha256: a45ef03e6e700cc6ac6c375e27904531cf8ade27eb3857e080537ff283fb0507
  md5: d27665b20bc4d074b86e628b3ba5ab8b
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - pango >=1.56.3,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  purls: []
  size: 6543651
  timestamp: 1743368725313
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/librsvg-2.58.4-h3ac5bce_3.conda
  sha256: e305cf09ec904625a66c7db1305595691c633276b7e34521537cef88edc5249a
  md5: b115c14b3919823fbe081366d2b15d86
  depends:
  - cairo >=1.18.4,<2.0a0
  - freetype >=2.13.3,<3.0a0
  - gdk-pixbuf >=2.42.12,<3.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libxml2 >=2.13.7,<2.14.0a0
  - pango >=1.56.3,<2.0a0
  constrains:
  - __glibc >=2.17
  license: LGPL-2.1-or-later
  purls: []
  size: 6274749
  timestamp: 1743376660664
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsndfile-1.2.2-hc60ed4a_1.conda
  sha256: f709cbede3d4f3aee4e2f8d60bd9e256057f410bd60b8964cb8cf82ec1457573
  md5: ef1910918dd895516a769ed36b5b3a4e
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 354372
  timestamp: 1695747735668
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsndfile-1.2.2-h79657aa_1.conda
  sha256: 8fcd5e45d6fb071e8baf492ebb8710203fd5eedf0cb791e007265db373c89942
  md5: ad8e62c0faec46b1442f960489c80b49
  depends:
  - lame >=3.100,<3.101.0a0
  - libflac >=1.4.3,<1.5.0a0
  - libgcc-ng >=12
  - libogg >=1.3.4,<1.4.0a0
  - libopus >=1.3.1,<2.0a0
  - libstdcxx-ng >=12
  - libvorbis >=1.3.7,<1.4.0a0
  - mpg123 >=1.32.1,<1.33.0a0
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 396501
  timestamp: 1695747749825
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsqlite-3.50.1-h6cd9bfd_7.conda
  sha256: 9a9e5bf30178f821d4f8de25eac0ae848915bfde6a78a66ae8b77d9c33d9d0e5
  md5: c7c4888059a8324e52de475d1e7bdc53
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 919723
  timestamp: 1750925531920
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsqlite-3.50.2-he2a92bd_0.conda
  sha256: 031499486870fa097445e660e3f1e94ba1e6c0dd063c9b1513be2ba5bff00472
  md5: d9c2f664f026418134d24a288eec2acd
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: Unlicense
  purls: []
  size: 918088
  timestamp: 1751135654429
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-15.1.0-h8f9b012_3.conda
  sha256: 7650837344b7850b62fdba02155da0b159cf472b9ab59eb7b472f7bd01dff241
  md5: 6d11a5edae89fe413c0569f16d308f5a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc 15.1.0 h767d61c_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 3896407
  timestamp: 1750808251302
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-15.1.0-h3f4de04_3.conda
  sha256: 916a8c2530992140d23c4d3f63502f250ff36df7298ed9a8b72d3629c347d4ce
  md5: 4e2d5a407e0ecfe493d8b2a65a437bd8
  depends:
  - libgcc 15.1.0 he277a41_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 3833339
  timestamp: 1750808947966
- conda: https://conda.anaconda.org/conda-forge/linux-64/libstdcxx-ng-15.1.0-h4852527_3.conda
  sha256: bbaea1ecf973a7836f92b8ebecc94d3c758414f4de39d2cc6818a3d10cb3216b
  md5: 57541755b5a51691955012b8e197c06c
  depends:
  - libstdcxx 15.1.0 h8f9b012_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  purls: []
  size: 29093
  timestamp: 1750808292700
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libstdcxx-ng-15.1.0-hf1166c9_3.conda
  sha256: cb93360dce004fda2f877fd6071c8c0d19ab67b161ff406d5c0d63b7658ad77c
  md5: f981af71cbd4c67c9e6acc7d4cc3f163
  depends:
  - libstdcxx 15.1.0 h3f4de04_3
  license: GPL-3.0-only WITH GCC-exception-3.1
  license_family: GPL
  purls: []
  size: 29078
  timestamp: 1750808974598
- conda: https://conda.anaconda.org/conda-forge/linux-64/libsystemd0-257.7-h4e0b6ca_0.conda
  sha256: e26b22c0ae40fb6ad4356104d5fa4ec33fe8dd8a10e6aef36a9ab0c6a6f47275
  md5: 1e12c8aa74fa4c3166a9bdc135bc4abf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 487969
  timestamp: 1750949895969
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libsystemd0-257.7-h2bb824b_0.conda
  sha256: 35ecfc98c22d4f035b051fe72398206607d48944e7bd4f60431e63eb95538e0d
  md5: 63b49a2d12a1739f72be430c2ed58727
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  - libgcrypt-lib >=1.11.1,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - lz4-c >=1.10.0,<1.11.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 510879
  timestamp: 1750949944203
- conda: https://conda.anaconda.org/conda-forge/linux-64/libtiff-4.7.0-hf01ce69_5.conda
  sha256: 7fa6ddac72e0d803bb08e55090a8f2e71769f1eb7adbd5711bdd7789561601b1
  md5: e79a094918988bb1807462cd42c83962
  depends:
  - __glibc >=2.17,<3.0.a0
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 429575
  timestamp: 1747067001268
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libtiff-4.7.0-h7c15681_5.conda
  sha256: 4b2c6f5cd5199d5e345228a0422ecb31a4340ff69579db49faccba14186bb9a2
  md5: 264a9aac20276b1784dac8c5f8d3704a
  depends:
  - lerc >=4.0.0,<5.0a0
  - libdeflate >=1.24,<1.25.0a0
  - libgcc >=13
  - libjpeg-turbo >=3.1.0,<4.0a0
  - liblzma >=5.8.1,<6.0a0
  - libstdcxx >=13
  - libwebp-base >=1.5.0,<2.0a0
  - libzlib >=1.3.1,<2.0a0
  - zstd >=1.5.7,<1.6.0a0
  license: HPND
  purls: []
  size: 466229
  timestamp: 1747067015512
- conda: https://conda.anaconda.org/conda-forge/linux-64/libudev1-257.7-hbe16f8c_0.conda
  sha256: 3fca2655f4cf2ce6b618a2b52e3dce92f27f429732b88080567d5bbeea404c82
  md5: 5a23e52bd654a5297bd3e247eebab5a3
  depends:
  - __glibc >=2.17,<3.0.a0
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 143533
  timestamp: 1750949902296
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libudev1-257.7-h7b9e449_0.conda
  sha256: 8c9847a934e251479f343f0be3b771836cdccfcf132145bd2da34946acd01988
  md5: d19d804623b40d7ab5f807c240b4caaf
  depends:
  - libcap >=2.75,<2.76.0a0
  - libgcc >=13
  license: LGPL-2.1-or-later
  purls: []
  size: 154447
  timestamp: 1750949949664
- conda: https://conda.anaconda.org/conda-forge/linux-64/libunwind-1.6.2-h9c3ff4c_0.tar.bz2
  sha256: f2ac872920833960e514ce9efd8f7c08ce66dd870738d73839d1bce1ac497de6
  md5: a730b2badd586580c5752cc73842e068
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  purls: []
  size: 75491
  timestamp: 1638450786937
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libunwind-1.6.2-h01db608_0.tar.bz2
  sha256: 7862d36ffc9f6b2ed3381ce77c78b9e5691d7353a19dd2050630868e192adf6f
  md5: 93b7bbf9099cfe09e67c0abe34bb7885
  depends:
  - libgcc-ng >=9.4.0
  - libstdcxx-ng >=9.4.0
  license: MIT
  license_family: MIT
  purls: []
  size: 90479
  timestamp: 1638452154070
- conda: https://conda.anaconda.org/conda-forge/linux-64/liburing-2.10-h84d6215_0.conda
  sha256: d1922de78ead6a9d19b7a4f82cf1fff7332e9012fd9968aa835c89888628d3d7
  md5: 1a11973f25f6168f4f6a65883cf7bb2a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 124944
  timestamp: 1748686602857
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/liburing-2.10-h17cf362_0.conda
  sha256: 658d4138e5cd76794c81c3561361fd9d2091c9a13a329cb5b61a83371feeef4e
  md5: 49a98ebf80bbc4661df8a26706d8b3b5
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 126811
  timestamp: 1748687566969
- conda: https://conda.anaconda.org/conda-forge/linux-64/libusb-1.0.29-h73b1eb8_0.conda
  sha256: 89c84f5b26028a9d0f5c4014330703e7dff73ba0c98f90103e9cef6b43a5323c
  md5: d17e3fb595a9f24fa9e149239a33475d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  purls: []
  size: 89551
  timestamp: 1748856210075
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libusb-1.0.29-h06eaf92_0.conda
  sha256: a60aae6b529cd7caa7842f9781ef95b93014e618f71fb005e404af434d76a33f
  md5: 9a86e7473e16fe25c5c47f6c1376ac82
  depends:
  - libgcc >=13
  - libudev1 >=257.4
  license: LGPL-2.1-or-later
  purls: []
  size: 93129
  timestamp: 1748856228398
- conda: https://conda.anaconda.org/conda-forge/linux-64/libuuid-2.38.1-h0b41bf4_0.conda
  sha256: 787eb542f055a2b3de553614b25f09eefb0a0931b0c87dbcce6efdfd92f04f18
  md5: 40b61aab5c7ba9ff276c41cfffe6b80b
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 33601
  timestamp: 1680112270483
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libuuid-2.38.1-hb4cce97_0.conda
  sha256: 616277b0c5f7616c2cdf36f6c316ea3f9aa5bb35f2d4476a349ab58b9b91675f
  md5: 000e30b09db0b7c775b21695dff30969
  depends:
  - libgcc-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 35720
  timestamp: 1680113474501
- conda: https://conda.anaconda.org/conda-forge/linux-64/libva-2.22.0-h4f16b4b_2.conda
  sha256: e0df324fb02fa05a05824b8db886b06659432b5cff39495c59e14a37aa23d40f
  md5: 2c65566e79dc11318ce689c656fb551c
  depends:
  - __glibc >=2.17,<3.0.a0
  - libdrm >=2.4.124,<2.5.0a0
  - libegl >=1.7.0,<2.0a0
  - libgcc >=13
  - libgl >=1.7.0,<2.0a0
  - libglx >=1.7.0,<2.0a0
  - libxcb >=1.17.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - wayland-protocols
  - xorg-libx11 >=1.8.11,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 217567
  timestamp: 1740897682004
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvorbis-1.3.7-h9c3ff4c_0.tar.bz2
  sha256: 53080d72388a57b3c31ad5805c93a7328e46ff22fab7c44ad2a86d712740af33
  md5: 309dec04b70a3cc0f1e84a4013683bc0
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 286280
  timestamp: 1610609811627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvorbis-1.3.7-h01db608_0.tar.bz2
  sha256: 1ade4727be5c52b287001b8094d02af66342dfe0ba13ef69222aaaf2e9be4342
  md5: c2863ff72c6d8a59054f8b9102c206e9
  depends:
  - libgcc-ng >=9.3.0
  - libogg >=1.3.4,<1.4.0a0
  - libstdcxx-ng >=9.3.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 292082
  timestamp: 1610616294416
- conda: https://conda.anaconda.org/conda-forge/linux-64/libvpx-1.14.1-hac33072_0.conda
  sha256: e7d2daf409c807be48310fcc8924e481b62988143f582eb3a58c5523a6763b13
  md5: cde393f461e0c169d9ffb2fc70f81c33
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1022466
  timestamp: 1717859935011
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libvpx-1.14.1-h0a1ffab_0.conda
  sha256: 918493354f78cb3bb2c3d91264afbcb312b2afe287237e7d1c85ee7e96d15b47
  md5: 3cb63f822a49e4c406639ebf8b5d87d7
  depends:
  - libgcc-ng >=12
  - libstdcxx-ng >=12
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1211700
  timestamp: 1717859955539
- conda: https://conda.anaconda.org/conda-forge/linux-64/libwebp-base-1.5.0-h851e524_0.conda
  sha256: c45283fd3e90df5f0bd3dbcd31f59cdd2b001d424cf30a07223655413b158eaf
  md5: 63f790534398730f59e1b899c3644d4a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 429973
  timestamp: 1734777489810
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libwebp-base-1.5.0-h0886dbf_0.conda
  sha256: b3d881a0ae08bb07fff7fa8ead506c8d2e0388733182fe4f216f3ec5d61ffcf0
  md5: 95ef4a689b8cc1b7e18b53784d88f96b
  depends:
  - libgcc >=13
  constrains:
  - libwebp 1.5.0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 362623
  timestamp: 1734779054659
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxcb-1.17.0-h8a09558_0.conda
  sha256: 666c0c431b23c6cec6e492840b176dde533d48b7e6fb8883f5071223433776aa
  md5: 92ed62436b625154323d40d5f2f11dd7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 395888
  timestamp: 1727278577118
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxcb-1.17.0-h262b8f6_0.conda
  sha256: 461cab3d5650ac6db73a367de5c8eca50363966e862dcf60181d693236b1ae7b
  md5: cd14ee5cca2464a425b1dbfc24d90db2
  depends:
  - libgcc >=13
  - pthread-stubs
  - xorg-libxau >=1.0.11,<2.0a0
  - xorg-libxdmcp
  license: MIT
  license_family: MIT
  purls: []
  size: 397493
  timestamp: 1727280745441
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxkbcommon-1.10.0-h65c71a3_0.conda
  sha256: a8043a46157511b3ceb6573a99952b5c0232313283f2d6a066cec7c8dcaed7d0
  md5: fedf6bfe5d21d21d2b1785ec00a8889a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 707156
  timestamp: 1747911059945
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxkbcommon-1.10.0-hbab7b08_0.conda
  sha256: 620f16864f4f9d7181d89fa4266dba0b18cb9bca72f930500cf9307e549e4247
  md5: 36cd1db31e923c6068b7e0e6fce2cd7b
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libxcb >=1.17.0,<2.0a0
  - libxml2 >=2.13.8,<2.14.0a0
  - xkeyboard-config
  - xorg-libxau >=1.0.12,<2.0a0
  license: MIT/X11 Derivative
  license_family: MIT
  purls: []
  size: 719116
  timestamp: 1747911079432
- conda: https://conda.anaconda.org/conda-forge/linux-64/libxml2-2.13.8-h4bc477f_0.conda
  sha256: b0b3a96791fa8bb4ec030295e8c8bf2d3278f33c0f9ad540e73b5e538e6268e7
  md5: 14dbe05b929e329dbaa6f2d0aa19466d
  depends:
  - __glibc >=2.17,<3.0.a0
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 690864
  timestamp: 1746634244154
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libxml2-2.13.8-he060846_0.conda
  sha256: b453be503923e213ce5132de0b2e2bc89549d742dcc403acba8dcc4a0ced740c
  md5: c73dfe6886cc8d39a09c357a36f91fb2
  depends:
  - icu >=75.1,<76.0a0
  - libgcc >=13
  - libiconv >=1.18,<2.0a0
  - liblzma >=5.8.1,<6.0a0
  - libzlib >=1.3.1,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 733785
  timestamp: 1746634366734
- conda: https://conda.anaconda.org/conda-forge/linux-64/libzlib-1.3.1-hb9d3cd8_2.conda
  sha256: d4bfe88d7cb447768e31650f06257995601f89076080e76df55e3112d4e47dc4
  md5: edb0dca6bc32e4f4789199455a1dbeb8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 60963
  timestamp: 1727963148474
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/libzlib-1.3.1-h86ecc28_2.conda
  sha256: 5a2c1eeef69342e88a98d1d95bff1603727ab1ff4ee0e421522acd8813439b84
  md5: 08aad7cbe9f5a6b460d0976076b6ae64
  depends:
  - libgcc >=13
  constrains:
  - zlib 1.3.1 *_2
  license: Zlib
  license_family: Other
  purls: []
  size: 66657
  timestamp: 1727963199518
- conda: https://conda.anaconda.org/conda-forge/linux-64/lz4-c-1.10.0-h5888daf_1.conda
  sha256: 47326f811392a5fd3055f0f773036c392d26fdb32e4d8e7a8197eed951489346
  md5: 9de5350a85c4a20c685259b889aa6393
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 167055
  timestamp: 1733741040117
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/lz4-c-1.10.0-h5ad3122_1.conda
  sha256: 67e55058d275beea76c1882399640c37b5be8be4eb39354c94b610928e9a0573
  md5: 6654e411da94011e8fbe004eacb8fe11
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 184953
  timestamp: 1733740984533
- pypi: https://files.pythonhosted.org/packages/42/d7/1ec15b46af6af88f19b8e5ffea08fa375d433c998b8a7639e76935c14f1f/markdown_it_py-3.0.0-py3-none-any.whl
  name: markdown-it-py
  version: 3.0.0
  sha256: 355216845c60bd96232cd8d8c40e8f9765cc86f46880e43a8fd22dc1a1a8cab1
  requires_dist:
  - mdurl~=0.1
  - psutil ; extra == 'benchmarking'
  - pytest ; extra == 'benchmarking'
  - pytest-benchmark ; extra == 'benchmarking'
  - pre-commit~=3.0 ; extra == 'code-style'
  - commonmark~=0.9 ; extra == 'compare'
  - markdown~=3.4 ; extra == 'compare'
  - mistletoe~=1.0 ; extra == 'compare'
  - mistune~=2.0 ; extra == 'compare'
  - panflute~=2.3 ; extra == 'compare'
  - linkify-it-py>=1,<3 ; extra == 'linkify'
  - mdit-py-plugins ; extra == 'plugins'
  - gprof2dot ; extra == 'profiling'
  - mdit-py-plugins ; extra == 'rtd'
  - myst-parser ; extra == 'rtd'
  - pyyaml ; extra == 'rtd'
  - sphinx ; extra == 'rtd'
  - sphinx-copybutton ; extra == 'rtd'
  - sphinx-design ; extra == 'rtd'
  - sphinx-book-theme ; extra == 'rtd'
  - jupyter-sphinx ; extra == 'rtd'
  - coverage ; extra == 'testing'
  - pytest ; extra == 'testing'
  - pytest-cov ; extra == 'testing'
  - pytest-regressions ; extra == 'testing'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/27/1a/1f68f9ba0c207934b35b86a8ca3aad8395a3d6dd7921c0686e23853ff5a9/mccabe-0.7.0-py2.py3-none-any.whl
  name: mccabe
  version: 0.7.0
  sha256: 6c2d30ab6be0e4a46919781807b4f0d834ebdd6c6e3dca0bda5a15f863427b6e
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/b3/38/89ba8ad64ae25be8de66a6d463314cf1eb366222074cfda9ee839c56a4b4/mdurl-0.1.2-py3-none-any.whl
  name: mdurl
  version: 0.1.2
  sha256: 84008a41e51615a49fc9966191ff91509e3c40b939176e643fd50a5c2196b8f8
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/mpg123-1.32.9-hc50e24c_0.conda
  sha256: 39c4700fb3fbe403a77d8cc27352fa72ba744db487559d5d44bf8411bb4ea200
  md5: c7f302fd11eeb0987a6a5e1f3aed6a21
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  purls: []
  size: 491140
  timestamp: 1730581373280
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/mpg123-1.32.9-h65af167_0.conda
  sha256: d65d5a00278544639ba4f99887154be00a1f57afb0b34d80b08e5cba40a17072
  md5: cdf140c7690ab0132106d3bc48bce47d
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: LGPL-2.1-only
  license_family: LGPL
  purls: []
  size: 558708
  timestamp: 1730581372400
- pypi: https://files.pythonhosted.org/packages/69/cb/b84afdb961dcf09b8e8c0238f068122d85480bfaac2c5c0b03120e497318/multidict-6.6.2-cp313-cp313-manylinux2014_x86_64.manylinux_2_17_x86_64.manylinux_2_28_x86_64.whl
  name: multidict
  version: 6.6.2
  sha256: c6c2d7686d2f9ecb18192455aa04345a05646f45a286d67b31b438eaf749a91e
  requires_dist:
  - typing-extensions>=4.1.0 ; python_full_version < '3.11'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/e7/ab/320d8507e7726c460cb77117848b3834ea0d59e769f36fdae495f7669929/multidict-6.6.3-cp313-cp313-manylinux2014_aarch64.manylinux_2_17_aarch64.manylinux_2_28_aarch64.whl
  name: multidict
  version: 6.6.3
  sha256: f3fc723ab8a5c5ed6c50418e9bfcd8e6dceba6c271cee6728a10a4ed8561520c
  requires_dist:
  - typing-extensions>=4.1.0 ; python_full_version < '3.11'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/b4/7e/81ca3b074021ad9775e5cb97ebe0089c0f13684b066a750b7dc208438403/mypy-1.16.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.manylinux_2_28_x86_64.whl
  name: mypy
  version: 1.16.1
  sha256: 051e1677689c9d9578b9c7f4d206d763f9bbd95723cd1416fad50db49d52f359
  requires_dist:
  - typing-extensions>=4.6.0
  - mypy-extensions>=1.0.0
  - pathspec>=0.9.0
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - psutil>=4.0 ; extra == 'dmypy'
  - setuptools>=50 ; extra == 'mypyc'
  - lxml ; extra == 'reports'
  - pip ; extra == 'install-types'
  - orjson ; extra == 'faster-cache'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c9/4f/c3c6b4b66374b5f68bab07c8cabd63a049ff69796b844bc759a0ca99bb2a/mypy-1.16.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.manylinux_2_28_aarch64.whl
  name: mypy
  version: 1.16.1
  sha256: 0a7cfb0fe29fe5a9841b7c8ee6dffb52382c45acdf68f032145b75620acfbd6f
  requires_dist:
  - typing-extensions>=4.6.0
  - mypy-extensions>=1.0.0
  - pathspec>=0.9.0
  - tomli>=1.1.0 ; python_full_version < '3.11'
  - psutil>=4.0 ; extra == 'dmypy'
  - setuptools>=50 ; extra == 'mypyc'
  - lxml ; extra == 'reports'
  - pip ; extra == 'install-types'
  - orjson ; extra == 'faster-cache'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/79/7b/2c79738432f5c924bef5071f933bcc9efd0473bac3b4aa584a6f7c1c8df8/mypy_extensions-1.1.0-py3-none-any.whl
  name: mypy-extensions
  version: 1.1.0
  sha256: 1be4cccdb0f2482337c4743e60421de3a356cd97508abadd57d47403e94f5505
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ncurses-6.5-h2d0b736_3.conda
  sha256: 3fde293232fa3fca98635e1167de6b7c7fda83caf24b9d6c91ec9eefb4f4d586
  md5: 47e340acb35de30501a76c7c799c41d7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 891641
  timestamp: 1738195959188
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/ncurses-6.5-ha32ae93_3.conda
  sha256: 91cfb655a68b0353b2833521dc919188db3d8a7f4c64bea2c6a7557b24747468
  md5: 182afabe009dc78d8b73100255ee6868
  depends:
  - libgcc >=13
  license: X11 AND BSD-3-Clause
  purls: []
  size: 926034
  timestamp: 1738196018799
- pypi: https://files.pythonhosted.org/packages/d2/1d/1b658dbd2b9fa9c4c9f32accbfc0205d532c8c6194dc0f2a4c0428e7128a/nodeenv-1.9.1-py2.py3-none-any.whl
  name: nodeenv
  version: 1.9.1
  sha256: ba11c9782d29c27c70ffbdda2d7415098754709be8a7056d79a737cd901155c9
  requires_python: '>=2.7,!=3.0.*,!=3.1.*,!=3.2.*,!=3.3.*,!=3.4.*,!=3.5.*,!=3.6.*'
- pypi: https://files.pythonhosted.org/packages/be/9c/92789c596b8df838baa98fa71844d84283302f7604ed565dafe5a6b5041a/oauthlib-3.3.1-py3-none-any.whl
  name: oauthlib
  version: 3.3.1
  sha256: 88119c938d2b8fb88561af5f6ee0eec8cc8d552b7bb1f712743136eb7523b7a1
  requires_dist:
  - cryptography>=3.0.0 ; extra == 'rsa'
  - cryptography>=3.0.0 ; extra == 'signedtoken'
  - pyjwt>=2.0.0,<3 ; extra == 'signedtoken'
  - blinker>=1.4.0 ; extra == 'signals'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/ocl-icd-2.3.3-hb9d3cd8_0.conda
  sha256: 2254dae821b286fb57c61895f2b40e3571a070910fdab79a948ff703e1ea807b
  md5: 56f8947aa9d5cf37b0b3d43b83f34192
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - opencl-headers >=2024.10.24
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 106742
  timestamp: 1743700382939
- conda: https://conda.anaconda.org/conda-forge/linux-64/opencl-headers-2025.06.13-h5888daf_0.conda
  sha256: 2b6ce54174ec19110e1b3c37455f7cd138d0e228a75727a9bba443427da30a36
  md5: 45c3d2c224002d6d0d7769142b29f986
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 55357
  timestamp: 1749853464518
- conda: https://conda.anaconda.org/conda-forge/linux-64/openh264-2.6.0-hc22cd8d_0.conda
  sha256: 3f231f2747a37a58471c82a9a8a80d92b7fece9f3fce10901a5ac888ce00b747
  md5: b28cf020fd2dead0ca6d113608683842
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 731471
  timestamp: 1739400677213
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openh264-2.6.0-h0564a2a_0.conda
  sha256: 3b7a519e3b7d7721a0536f6cba7f1909b878c71962ee67f02242958314748341
  md5: 0abed5d78c07a64e85c54f705ba14d30
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 774512
  timestamp: 1739400731652
- conda: https://conda.anaconda.org/conda-forge/linux-64/openssl-3.5.0-h7b32b05_1.conda
  sha256: b4491077c494dbf0b5eaa6d87738c22f2154e9277e5293175ec187634bd808a0
  md5: de356753cfdbffcde5bb1e86e3aa6cd0
  depends:
  - __glibc >=2.17,<3.0.a0
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3117410
  timestamp: 1746223723843
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/openssl-3.5.1-hd08dc88_0.conda
  sha256: 0b4f88052fc9c14aa17c844d1e92a9a76277aa980a445a47d2dbc6590d60a991
  md5: cf2dfe9c774c20e65d42d87147903bdb
  depends:
  - ca-certificates
  - libgcc >=13
  license: Apache-2.0
  license_family: Apache
  purls: []
  size: 3653877
  timestamp: 1751392052717
- pypi: https://files.pythonhosted.org/packages/20/12/38679034af332785aac8774540895e234f4d07f7545804097de4b666afd8/packaging-25.0-py3-none-any.whl
  name: packaging
  version: '25.0'
  sha256: 29572ef2b1f17581046b3a2227d5c611fb25ec70ca1ba8554b24b0e69331a484
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pango-1.56.3-h9ac818e_1.conda
  sha256: 9c00bbc8871b9ce00d1a1f0c1a64f76c032cf16a56a28984b9bb59e46af3932d
  md5: 21899b96828014270bd24fd266096612
  depends:
  - __glibc >=2.17,<3.0.a0
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - freetype >=2.13.3,<3.0a0
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=11.0.0,<12.0a0
  - libexpat >=2.6.4,<3.0a0
  - libgcc >=13
  - libglib >=2.84.0,<3.0a0
  - libpng >=1.6.47,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 453100
  timestamp: 1743352484196
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pango-1.56.4-he55ef5b_0.conda
  sha256: dd36cd5b6bc1c2988291a6db9fa4eb8acade9b487f6f1da4eaa65a1eebb0a12d
  md5: a22cc88bf6059c9bcc158c94c9aab5b8
  depends:
  - cairo >=1.18.4,<2.0a0
  - fontconfig >=2.15.0,<3.0a0
  - fonts-conda-ecosystem
  - fribidi >=1.0.10,<2.0a0
  - harfbuzz >=11.0.1
  - libexpat >=2.7.0,<3.0a0
  - libfreetype >=2.13.3
  - libfreetype6 >=2.13.3
  - libgcc >=13
  - libglib >=2.84.2,<3.0a0
  - libpng >=1.6.49,<1.7.0a0
  - libzlib >=1.3.1,<2.0a0
  license: LGPL-2.1-or-later
  purls: []
  size: 468811
  timestamp: 1751293869070
- pypi: https://files.pythonhosted.org/packages/cc/20/ff623b09d963f88bfde16306a54e12ee5ea43e9b597108672ff3a408aad6/pathspec-0.12.1-py3-none-any.whl
  name: pathspec
  version: 0.12.1
  sha256: a0d503e138a4c123b27490a4f7beda6a01c6f288df0e4a8b79c7eb0dc7b4cc08
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pcre2-10.45-hc749103_0.conda
  sha256: 27c4014f616326240dcce17b5f3baca3953b6bc5f245ceb49c3fa1e6320571eb
  md5: b90bece58b4c2bf25969b70f3be42d25
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1197308
  timestamp: 1745955064657
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pcre2-10.45-hf4ec17f_0.conda
  sha256: d5aecfcb64514719600e35290cc885098dbfef8e9c037eea6afc43d1acc65c2e
  md5: ad22a9a9497f7aedce73e0da53cd215f
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 1134832
  timestamp: 1745955178803
- pypi: https://files.pythonhosted.org/packages/ba/c9/09e6746630fe6372c67c648ff9deae52a2bc20897d51fa293571977ceb5d/pillow-11.3.0-cp313-cp313-manylinux_2_27_aarch64.manylinux_2_28_aarch64.whl
  name: pillow
  version: 11.3.0
  sha256: c37d8ba9411d6003bba9e518db0db0c58a680ab9fe5179f040b0463644bc9805
  requires_dist:
  - furo ; extra == 'docs'
  - olefile ; extra == 'docs'
  - sphinx>=8.2 ; extra == 'docs'
  - sphinx-autobuild ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinx-inline-tabs ; extra == 'docs'
  - sphinxext-opengraph ; extra == 'docs'
  - olefile ; extra == 'fpx'
  - olefile ; extra == 'mic'
  - pyarrow ; extra == 'test-arrow'
  - check-manifest ; extra == 'tests'
  - coverage>=7.4.2 ; extra == 'tests'
  - defusedxml ; extra == 'tests'
  - markdown2 ; extra == 'tests'
  - olefile ; extra == 'tests'
  - packaging ; extra == 'tests'
  - pyroma ; extra == 'tests'
  - pytest ; extra == 'tests'
  - pytest-cov ; extra == 'tests'
  - pytest-timeout ; extra == 'tests'
  - pytest-xdist ; extra == 'tests'
  - trove-classifiers>=2024.10.12 ; extra == 'tests'
  - typing-extensions ; python_full_version < '3.10' and extra == 'typing'
  - defusedxml ; extra == 'xmp'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/d5/1c/a2a29649c0b1983d3ef57ee87a66487fdeb45132df66ab30dd37f7dbe162/pillow-11.3.0-cp313-cp313-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl
  name: pillow
  version: 11.3.0
  sha256: 13f87d581e71d9189ab21fe0efb5a23e9f28552d5be6979e84001d3b8505abe8
  requires_dist:
  - furo ; extra == 'docs'
  - olefile ; extra == 'docs'
  - sphinx>=8.2 ; extra == 'docs'
  - sphinx-autobuild ; extra == 'docs'
  - sphinx-copybutton ; extra == 'docs'
  - sphinx-inline-tabs ; extra == 'docs'
  - sphinxext-opengraph ; extra == 'docs'
  - olefile ; extra == 'fpx'
  - olefile ; extra == 'mic'
  - pyarrow ; extra == 'test-arrow'
  - check-manifest ; extra == 'tests'
  - coverage>=7.4.2 ; extra == 'tests'
  - defusedxml ; extra == 'tests'
  - markdown2 ; extra == 'tests'
  - olefile ; extra == 'tests'
  - packaging ; extra == 'tests'
  - pyroma ; extra == 'tests'
  - pytest ; extra == 'tests'
  - pytest-cov ; extra == 'tests'
  - pytest-timeout ; extra == 'tests'
  - pytest-xdist ; extra == 'tests'
  - trove-classifiers>=2024.10.12 ; extra == 'tests'
  - typing-extensions ; python_full_version < '3.10' and extra == 'typing'
  - defusedxml ; extra == 'xmp'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pixman-0.46.2-h29eaf8c_0.conda
  sha256: 6cb261595b5f0ae7306599f2bb55ef6863534b6d4d1bc0dcfdfa5825b0e4e53d
  md5: 39b4228a867772d610c02e06f939a5b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 402222
  timestamp: 1749552884791
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pixman-0.46.2-h86a87f0_0.conda
  sha256: df60bb320bbec8df804780c0310b471478a245192c16568769fc96269ce15445
  md5: 019114cf59c0cce5a08f6661179a1d65
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 310404
  timestamp: 1749554318638
- pypi: https://files.pythonhosted.org/packages/fe/39/979e8e21520d4e47a0bbe349e2713c0aac6f3d853d0e5b34d76206c439aa/platformdirs-4.3.8-py3-none-any.whl
  name: platformdirs
  version: 4.3.8
  sha256: ff7059bb7eb1179e2685604f4aaf157cfd9535242bd23742eadc3c13542139b4
  requires_dist:
  - furo>=2024.8.6 ; extra == 'docs'
  - proselint>=0.14 ; extra == 'docs'
  - sphinx-autodoc-typehints>=3 ; extra == 'docs'
  - sphinx>=8.1.3 ; extra == 'docs'
  - appdirs==1.4.4 ; extra == 'test'
  - covdefaults>=2.3 ; extra == 'test'
  - pytest-cov>=6 ; extra == 'test'
  - pytest-mock>=3.14 ; extra == 'test'
  - pytest>=8.3.4 ; extra == 'test'
  - mypy>=1.14.1 ; extra == 'type'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/54/20/4d324d65cc6d9205fabedc306948156824eb9f0ee1633355a8f7ec5c66bf/pluggy-1.6.0-py3-none-any.whl
  name: pluggy
  version: 1.6.0
  sha256: e920276dd6813095e9377c0bc5566d94c932c33b27a3e3945d8389c374dd4746
  requires_dist:
  - pre-commit ; extra == 'dev'
  - tox ; extra == 'dev'
  - pytest ; extra == 'testing'
  - pytest-benchmark ; extra == 'testing'
  - coverage ; extra == 'testing'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/88/74/a88bf1b1efeae488a0c0b7bdf71429c313722d1fc0f377537fbe554e6180/pre_commit-4.2.0-py2.py3-none-any.whl
  name: pre-commit
  version: 4.2.0
  sha256: a009ca7205f1eb497d10b845e52c838a98b6cdd2102a6c8e4540e94ee75c58bd
  requires_dist:
  - cfgv>=2.0.0
  - identify>=1.0.0
  - nodeenv>=0.11.1
  - pyyaml>=5.1
  - virtualenv>=20.10.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/5b/ad/3f0f9a705fb630d175146cd7b1d2bf5555c9beaed54e94132b21aac098a6/propcache-0.3.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: propcache
  version: 0.3.2
  sha256: d1a342c834734edb4be5ecb1e9fb48cb64b1e2320fccbd8c54bf8da8f2a84c33
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/af/81/b324c44ae60c56ef12007105f1460d5c304b0626ab0cc6b07c8f2a9aa0b8/propcache-0.3.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: propcache
  version: 0.3.2
  sha256: 4c1396592321ac83157ac03a2023aa6cc4a3cc3cfdecb71090054c09e5a7cce3
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/4e/6d/280c4c2ce28b1593a19ad5239c8b826871fc6ec275c21afc8e1820108039/proto_plus-1.26.1-py3-none-any.whl
  name: proto-plus
  version: 1.26.1
  sha256: 13285478c2dcf2abb829db158e1047e2f1e8d63a077d94263c2b88b043c75a66
  requires_dist:
  - protobuf>=3.19.0,<7.0.0
  - google-api-core>=1.31.5 ; extra == 'testing'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/76/a1/7a5a94032c83375e4fe7e7f56e3976ea6ac90c5e85fac8576409e25c39c3/protobuf-6.31.1-cp39-abi3-manylinux2014_aarch64.whl
  name: protobuf
  version: 6.31.1
  sha256: a40fc12b84c154884d7d4c4ebd675d5b3b5283e155f324049ae396b95ddebc39
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/fa/b1/b59d405d64d31999244643d88c45c8241c58f17cc887e73bcb90602327f8/protobuf-6.31.1-cp39-abi3-manylinux2014_x86_64.whl
  name: protobuf
  version: 6.31.1
  sha256: 4ee898bf66f7a8b0bd21bce523814e6fbd8c6add948045ce958b73af7e8878c6
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/pthread-stubs-0.4-hb9d3cd8_1002.conda
  sha256: 9c88f8c64590e9567c6c80823f0328e58d3b1efb0e1c539c0315ceca764e0973
  md5: b3c17d95b5a10c6e64a21fa17573e70e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8252
  timestamp: 1726802366959
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pthread-stubs-0.4-h86ecc28_1002.conda
  sha256: 977dfb0cb3935d748521dd80262fe7169ab82920afd38ed14b7fee2ea5ec01ba
  md5: bb5a90c93e3bac3d5690acf76b4a6386
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 8342
  timestamp: 1726803319942
- conda: https://conda.anaconda.org/conda-forge/linux-64/pugixml-1.15-h3f63f65_0.conda
  sha256: 23c98a5000356e173568dc5c5770b53393879f946f3ace716bbdefac2a8b23d2
  md5: b11a4c6bf6f6f44e5e143f759ffa2087
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 118488
  timestamp: 1736601364156
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pugixml-1.15-h6ef32b0_0.conda
  sha256: adc17205a87e064508d809fe5542b7cf49f9b9a458418f8448e2fc895fcd04f3
  md5: 53e14f45d38558aa2b9a15b07416e472
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 113424
  timestamp: 1737355438448
- conda: https://conda.anaconda.org/conda-forge/linux-64/pulseaudio-client-17.0-hac146a9_1.conda
  sha256: d2377bb571932f2373f593b7b2fc3b9728dc6ae5b993b1b65d7f2c8bb39a0b49
  md5: 66b1fa9608d8836e25f9919159adc9c6
  depends:
  - __glibc >=2.17,<3.0.a0
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 764231
  timestamp: 1742507189208
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/pulseaudio-client-17.0-h2f84921_1.conda
  sha256: 0294728d0a2fc0bdfbcfda98b7ada2d8bb420f76c944fa041ece4140858c2ee5
  md5: a617203ec445f510a3a8651810c735e0
  depends:
  - dbus >=1.13.6,<2.0a0
  - libgcc >=13
  - libglib >=2.82.2,<3.0a0
  - libiconv >=1.18,<2.0a0
  - libsndfile >=1.2.2,<1.3.0a0
  - libsystemd0 >=257.4
  - libxcb >=1.17.0,<2.0a0
  constrains:
  - pulseaudio 17.0 *_1
  license: LGPL-2.1-or-later
  license_family: LGPL
  purls: []
  size: 763814
  timestamp: 1742507234837
- pypi: https://files.pythonhosted.org/packages/c8/f1/d6a797abb14f6283c0ddff96bbdd46937f64122b8c925cab503dd37f8214/pyasn1-0.6.1-py3-none-any.whl
  name: pyasn1
  version: 0.6.1
  sha256: 0d632f46f2ba09143da3a8afe9e33fb6f92fa2320ab7e886e2d0f7672af84629
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/47/8d/d529b5d697919ba8c11ad626e835d4039be708a35b0d22de83a269a6682c/pyasn1_modules-0.4.2-py3-none-any.whl
  name: pyasn1-modules
  version: 0.4.2
  sha256: 29253a9207ce32b64c3ac6600edc75368f98473906e8fd1043bd6b5b1de2c14a
  requires_dist:
  - pyasn1>=0.6.1,<0.7.0
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/d7/27/a58ddaf8c588a3ef080db9d0b7e0b97215cee3a45df74f3a94dbbf5c893a/pycodestyle-2.14.0-py2.py3-none-any.whl
  name: pycodestyle
  version: 2.14.0
  sha256: dd6bf7cb4ee77f8e016f9c8e74a35ddd9f67e1d5fd4184d86c3b98e07099f42d
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/6a/c0/ec2b1c8712ca690e5d61979dee872603e92b8a32f94cc1b72d53beab008a/pydantic-2.11.7-py3-none-any.whl
  name: pydantic
  version: 2.11.7
  sha256: dde5df002701f6de26248661f6835bbe296a47bf73990135c7d07ce741b9623b
  requires_dist:
  - annotated-types>=0.6.0
  - pydantic-core==2.33.2
  - typing-extensions>=4.12.2
  - typing-inspection>=0.4.0
  - email-validator>=2.0.0 ; extra == 'email'
  - tzdata ; python_full_version >= '3.9' and sys_platform == 'win32' and extra == 'timezone'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/6f/5e/a0a7b8885c98889a18b6e376f344da1ef323d270b44edf8174d6bce4d622/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 0a9f2c9dd19656823cb8250b0724ee9c60a82f3cdf68a080979d13092a3b0fef
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/eb/3c/f4abd740877a35abade05e437245b192f9d0ffb48bbbbd708df33d3cda37/pydantic_core-2.33.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pydantic-core
  version: 2.33.2
  sha256: 9fdac5d6ffa1b5a83bca06ffe7583f5576555e6c8b3a91fbd25ea7780f825f7d
  requires_dist:
  - typing-extensions>=4.6.0,!=4.7.0
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/58/f0/427018098906416f580e3cf1366d3b1abfb408a0652e9f31600c24a1903c/pydantic_settings-2.10.1-py3-none-any.whl
  name: pydantic-settings
  version: 2.10.1
  sha256: a60952460b99cf661dc25c29c0ef171721f98bfcb52ef8d9ea4c943d7c8cc796
  requires_dist:
  - pydantic>=2.7.0
  - python-dotenv>=0.21.0
  - typing-inspection>=0.4.0
  - boto3-stubs[secretsmanager] ; extra == 'aws-secrets-manager'
  - boto3>=1.35.0 ; extra == 'aws-secrets-manager'
  - azure-identity>=1.16.0 ; extra == 'azure-key-vault'
  - azure-keyvault-secrets>=4.8.0 ; extra == 'azure-key-vault'
  - google-cloud-secret-manager>=2.23.1 ; extra == 'gcp-secret-manager'
  - tomli>=2.0.1 ; extra == 'toml'
  - pyyaml>=6.0.1 ; extra == 'yaml'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c2/2f/81d580a0fb83baeb066698975cb14a618bdbed7720678566f1b046a95fe8/pyflakes-3.4.0-py2.py3-none-any.whl
  name: pyflakes
  version: 3.4.0
  sha256: f742a7dbd0d9cb9ea41e9a24a918996e8170c799fa528688d40dd582c8265f4f
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/c7/21/705964c7812476f378728bdf590ca4b771ec72385c533964653c68e86bdc/pygments-2.19.2-py3-none-any.whl
  name: pygments
  version: 2.19.2
  sha256: 86540386c03d588bb81d44bc3928634ff26449851e99741617ecb9037ee5ec0b
  requires_dist:
  - colorama>=0.4.6 ; extra == 'windows-terminal'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/05/e7/df2285f3d08fee213f2d041540fa4fc9ca6c2d44cf36d3a035bf2a8d2bcc/pyparsing-3.2.3-py3-none-any.whl
  name: pyparsing
  version: 3.2.3
  sha256: a749938e02d6fd0b59b356ca504a24982314bb090c383e3cf201c95ef7e2bfcf
  requires_dist:
  - railroad-diagrams ; extra == 'diagrams'
  - jinja2 ; extra == 'diagrams'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/29/16/c8a903f4c4dffe7a12843191437d7cd8e32751d5de349d45d3fe69544e87/pytest-8.4.1-py3-none-any.whl
  name: pytest
  version: 8.4.1
  sha256: 539c70ba6fcead8e78eebbf1115e8b589e7565830d7d006a8723f19ac8a0afb7
  requires_dist:
  - colorama>=0.4 ; sys_platform == 'win32'
  - exceptiongroup>=1 ; python_full_version < '3.11'
  - iniconfig>=1
  - packaging>=20
  - pluggy>=1.5,<2
  - pygments>=2.7.2
  - tomli>=1 ; python_full_version < '3.11'
  - argcomplete ; extra == 'dev'
  - attrs>=19.2 ; extra == 'dev'
  - hypothesis>=3.56 ; extra == 'dev'
  - mock ; extra == 'dev'
  - requests ; extra == 'dev'
  - setuptools ; extra == 'dev'
  - xmlschema ; extra == 'dev'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/30/05/ce271016e351fddc8399e546f6e23761967ee09c8c568bbfbecb0c150171/pytest_asyncio-1.0.0-py3-none-any.whl
  name: pytest-asyncio
  version: 1.0.0
  sha256: 4f024da9f1ef945e680dc68610b52550e36590a67fd31bb3b4943979a1f90ef3
  requires_dist:
  - pytest>=8.2,<9
  - typing-extensions>=4.12 ; python_full_version < '3.10'
  - sphinx>=5.3 ; extra == 'docs'
  - sphinx-rtd-theme>=1 ; extra == 'docs'
  - coverage>=6.2 ; extra == 'testing'
  - hypothesis>=5.7.1 ; extra == 'testing'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/python-3.13.5-hec9711d_102_cp313.conda
  build_number: 102
  sha256: c2cdcc98ea3cbf78240624e4077e164dc9d5588eefb044b4097c3df54d24d504
  md5: 89e07d92cf50743886f41638d58c4328
  depends:
  - __glibc >=2.17,<3.0.a0
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  purls: []
  size: 33273132
  timestamp: 1750064035176
  python_site_packages_path: lib/python3.13/site-packages
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/python-3.13.5-h2382df9_102_cp313.conda
  build_number: 102
  sha256: 2eb3ce8b2acf036bd30d4d41cfb45766ad817e26479f18177cfb950c0af6f27b
  md5: ed5b16381ac28233a65c549a59d97b68
  depends:
  - bzip2 >=1.0.8,<2.0a0
  - ld_impl_linux-aarch64 >=2.36.1
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - liblzma >=5.8.1,<6.0a0
  - libmpdec >=4.0.0,<5.0a0
  - libsqlite >=3.50.1,<4.0a0
  - libuuid >=2.38.1,<3.0a0
  - libzlib >=1.3.1,<2.0a0
  - ncurses >=6.5,<7.0a0
  - openssl >=3.5.0,<4.0a0
  - python_abi 3.13.* *_cp313
  - readline >=8.2,<9.0a0
  - tk >=8.6.13,<8.7.0a0
  - tzdata
  license: Python-2.0
  purls: []
  size: 33764400
  timestamp: 1750062474929
  python_site_packages_path: lib/python3.13/site-packages
- pypi: https://files.pythonhosted.org/packages/5f/ed/539768cf28c661b5b068d66d96a2f155c4971a5d55684a514c1a0e0dec2f/python_dotenv-1.1.1-py3-none-any.whl
  name: python-dotenv
  version: 1.1.1
  sha256: 31f23644fe2602f88ff55e1f5c79ba497e01224ee7737937930c448e4d0e24dc
  requires_dist:
  - click>=5.0 ; extra == 'cli'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/python_abi-3.13-7_cp313.conda
  build_number: 7
  sha256: 0595134584589064f56e67d3de1d8fcbb673a972946bce25fb593fb092fdcd97
  md5: e84b44e6300f1703cb25d29120c5b1d8
  constrains:
  - python 3.13.* *_cp313
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 6988
  timestamp: 1745258852285
- pypi: https://files.pythonhosted.org/packages/04/24/b7721e4845c2f162d26f50521b825fb061bc0a5afcf9a386840f23ea19fa/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: pyyaml
  version: 6.0.2
  sha256: 70b189594dbe54f75ab3a1acec5f1e3faa7e8cf2f1e08d9b561cb41b845f69d5
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/7c/9a/337322f27005c33bcb656c655fa78325b730324c78620e8328ae28b64d0c/PyYAML-6.0.2-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: pyyaml
  version: 6.0.2
  sha256: 0ffe8360bab4910ef1b9e87fb812d8bc0a308b0d0eef8c8f44e0254ab3b07133
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/readline-8.2-h8c095d6_2.conda
  sha256: 2d6d0c026902561ed77cd646b5021aef2d4db22e57a5b0178dfc669231e06d2c
  md5: 283b96675859b20a825f8fa30f311446
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 282480
  timestamp: 1740379431762
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/readline-8.2-h8382b9d_2.conda
  sha256: 54bed3a3041befaa9f5acde4a37b1a02f44705b7796689574bcf9d7beaad2959
  md5: c0f08fc2737967edde1a272d4bf41ed9
  depends:
  - libgcc >=13
  - ncurses >=6.5,<7.0a0
  license: GPL-3.0-only
  license_family: GPL
  purls: []
  size: 291806
  timestamp: 1740380591358
- pypi: https://files.pythonhosted.org/packages/7c/e4/56027c4a6b4ae70ca9de302488c5ca95ad4a39e190093d6c1a8ace08341b/requests-2.32.4-py3-none-any.whl
  name: requests
  version: 2.32.4
  sha256: 27babd3cda2a6d50b30443204ee89830707d396671944c998b5975b031ac2b2c
  requires_dist:
  - charset-normalizer>=2,<4
  - idna>=2.5,<4
  - urllib3>=1.21.1,<3
  - certifi>=2017.4.17
  - pysocks>=1.5.6,!=1.5.7 ; extra == 'socks'
  - chardet>=3.0.2,<6 ; extra == 'use-chardet-on-py3'
  requires_python: '>=3.8'
- pypi: https://files.pythonhosted.org/packages/3b/5d/63d4ae3b9daea098d5d6f5da83984853c1bbacd5dc826764b249fe119d24/requests_oauthlib-2.0.0-py2.py3-none-any.whl
  name: requests-oauthlib
  version: 2.0.0
  sha256: 7dd8a5c40426b779b0868c404bdef9768deccf22749cde15852df527e6269b36
  requires_dist:
  - oauthlib>=3.0.0
  - requests>=2.0.0
  - oauthlib[signedtoken]>=3.0.0 ; extra == 'rsa'
  requires_python: '>=3.4'
- pypi: https://files.pythonhosted.org/packages/0d/9b/63f4c7ebc259242c89b3acafdb37b41d1185c07ff0011164674e9076b491/rich-14.0.0-py3-none-any.whl
  name: rich
  version: 14.0.0
  sha256: 1c9491e1951aac09caffd42f448ee3d04e58923ffe14993f6e83068dc395d7e0
  requires_dist:
  - ipywidgets>=7.5.1,<9 ; extra == 'jupyter'
  - markdown-it-py>=2.2.0
  - pygments>=2.13.0,<3.0.0
  - typing-extensions>=4.0.0,<5.0 ; python_full_version < '3.11'
  requires_python: '>=3.8.0'
- pypi: https://files.pythonhosted.org/packages/64/8d/0133e4eb4beed9e425d9a98ed6e081a55d195481b7632472be1af08d2f6b/rsa-4.9.1-py3-none-any.whl
  name: rsa
  version: 4.9.1
  sha256: 68635866661c6836b8d39430f97a996acbd61bfa49406748ea243539fe239762
  requires_dist:
  - pyasn1>=0.1.3
  requires_python: '>=3.6,<4'
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl2-2.32.54-h3f2d84a_0.conda
  sha256: 7cd82ca1d1989de6ac28e72ba0bfaae1c055278f931b0c7ef51bb1abba3ddd2f
  md5: 91f8537d64c4d52cbbb2910e8bd61bd2
  depends:
  - libgcc >=13
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - sdl3 >=3.2.10,<4.0a0
  - libgl >=1.7.0,<2.0a0
  - libegl >=1.7.0,<2.0a0
  license: Zlib
  purls: []
  size: 587053
  timestamp: 1745799881584
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl2-2.32.54-h5ad3122_0.conda
  sha256: d83c13fc35ed447d186150d32b8bc48bdd73a047280ba6e06f151d4cce52639d
  md5: 6b38021cb802b4e5bede7fe38c547883
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libegl >=1.7.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - sdl3 >=3.2.10,<4.0a0
  license: Zlib
  purls: []
  size: 597383
  timestamp: 1745799910298
- conda: https://conda.anaconda.org/conda-forge/linux-64/sdl3-3.2.16-he3e324a_0.conda
  sha256: 7fe5ff84801d1ad0713efbb1a9c39c3c4245ccee5586bd62fc4604d0f23ce0df
  md5: c3ab38fdbcf36625620c9a4df786320a
  depends:
  - __glibc >=2.17,<3.0.a0
  - libstdcxx >=13
  - libgcc >=13
  - dbus >=1.16.2,<2.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libxkbcommon >=1.10.0,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - wayland >=1.23.1,<2.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - libegl >=1.7.0,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxscrnsaver >=1.2.4,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - libunwind >=1.6.2,<1.7.0a0
  license: Zlib
  purls: []
  size: 1941645
  timestamp: 1748911618893
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/sdl3-3.2.16-h7e2c5d6_0.conda
  sha256: 782c6ac4f96e2b75cf55afdd223ffd043fc459a5a7c7b460c4077764733d5bd9
  md5: 37fbfe4c4baa10eca13823b535ec5d1a
  depends:
  - libstdcxx >=13
  - libgcc >=13
  - libxkbcommon >=1.10.0,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  - xorg-libxcursor >=1.2.3,<2.0a0
  - libunwind >=1.6.2,<1.7.0a0
  - libusb >=1.0.29,<2.0a0
  - libudev1 >=257.6
  - wayland >=1.23.1,<2.0a0
  - libgl >=1.7.0,<2.0a0
  - pulseaudio-client >=17.0,<17.1.0a0
  - dbus >=1.16.2,<2.0a0
  - liburing >=2.10,<2.11.0a0
  - libegl >=1.7.0,<2.0a0
  - libdrm >=2.4.124,<2.5.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libx11 >=1.8.12,<2.0a0
  license: Zlib
  purls: []
  size: 1899812
  timestamp: 1748911660322
- pypi: https://files.pythonhosted.org/packages/01/a1/fc4856bd02d2097324fb7ce05b3021fb850f864b83ca765f6e37e92ff8ca/sentry_sdk-2.32.0-py2.py3-none-any.whl
  name: sentry-sdk
  version: 2.32.0
  sha256: ****************************************************************
  requires_dist:
  - urllib3>=1.26.11
  - certifi
  - aiohttp>=3.5 ; extra == 'aiohttp'
  - anthropic>=0.16 ; extra == 'anthropic'
  - arq>=0.23 ; extra == 'arq'
  - asyncpg>=0.23 ; extra == 'asyncpg'
  - apache-beam>=2.12 ; extra == 'beam'
  - bottle>=0.12.13 ; extra == 'bottle'
  - celery>=3 ; extra == 'celery'
  - celery-redbeat>=2 ; extra == 'celery-redbeat'
  - chalice>=1.16.0 ; extra == 'chalice'
  - clickhouse-driver>=0.2.0 ; extra == 'clickhouse-driver'
  - django>=1.8 ; extra == 'django'
  - falcon>=1.4 ; extra == 'falcon'
  - fastapi>=0.79.0 ; extra == 'fastapi'
  - flask>=0.11 ; extra == 'flask'
  - blinker>=1.1 ; extra == 'flask'
  - markupsafe ; extra == 'flask'
  - grpcio>=1.21.1 ; extra == 'grpcio'
  - protobuf>=3.8.0 ; extra == 'grpcio'
  - httpcore[http2]==1.* ; extra == 'http2'
  - httpx>=0.16.0 ; extra == 'httpx'
  - huey>=2 ; extra == 'huey'
  - huggingface-hub>=0.22 ; extra == 'huggingface-hub'
  - langchain>=0.0.210 ; extra == 'langchain'
  - launchdarkly-server-sdk>=9.8.0 ; extra == 'launchdarkly'
  - litestar>=2.0.0 ; extra == 'litestar'
  - loguru>=0.5 ; extra == 'loguru'
  - openai>=1.0.0 ; extra == 'openai'
  - tiktoken>=0.3.0 ; extra == 'openai'
  - openfeature-sdk>=0.7.1 ; extra == 'openfeature'
  - opentelemetry-distro>=0.35b0 ; extra == 'opentelemetry'
  - opentelemetry-distro ; extra == 'opentelemetry-experimental'
  - pure-eval ; extra == 'pure-eval'
  - executing ; extra == 'pure-eval'
  - asttokens ; extra == 'pure-eval'
  - pymongo>=3.1 ; extra == 'pymongo'
  - pyspark>=2.4.4 ; extra == 'pyspark'
  - quart>=0.16.1 ; extra == 'quart'
  - blinker>=1.1 ; extra == 'quart'
  - rq>=0.6 ; extra == 'rq'
  - sanic>=0.8 ; extra == 'sanic'
  - sqlalchemy>=1.2 ; extra == 'sqlalchemy'
  - starlette>=0.19.1 ; extra == 'starlette'
  - starlite>=1.48 ; extra == 'starlite'
  - statsig>=0.55.3 ; extra == 'statsig'
  - tornado>=6 ; extra == 'tornado'
  - unleashclient>=6.0.1 ; extra == 'unleash'
  requires_python: '>=3.6'
- pypi: https://files.pythonhosted.org/packages/e0/f9/0595336914c5619e5f28a1fb793285925a8cd4b432c9da0a987836c7f822/shellingham-1.5.4-py2.py3-none-any.whl
  name: shellingham
  version: 1.5.4
  sha256: 7ecfff8f2fd72616f7481040475a65b2bf8af90a56c89140852d1120324e8686
  requires_python: '>=3.7'
- conda: https://conda.anaconda.org/conda-forge/linux-64/snappy-1.2.1-h8bd8927_1.conda
  sha256: ec91e86eeb2c6bbf09d51351b851e945185d70661d2ada67204c9a6419d282d3
  md5: 3b3e64af585eadfb52bb90b553db5edf
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 42739
  timestamp: 1733501881851
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/snappy-1.2.1-hd4fb6f5_1.conda
  sha256: c4a07ae5def8d55128f25a567a296ef9d7bf99a3bc79d46bd5160c076a5f50af
  md5: 2fcc6cd1e5550deb509073fd2e6693e1
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 43032
  timestamp: 1733501964775
- pypi: https://files.pythonhosted.org/packages/e9/44/75a9c9421471a6c4805dbf2356f7c181a29c1879239abab1ea2cc8f38b40/sniffio-1.3.1-py3-none-any.whl
  name: sniffio
  version: 1.3.1
  sha256: 2f6da418d1f1e0fddd844478f41680e794e6051915791a034ff65e5f100525a2
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/5e/51/5ba9ea3246ea068630acf35a6ba0d181e99f1af1afd17e159eac7e8bc2b8/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: sqlalchemy
  version: 2.0.41
  sha256: dc56c9788617b8964ad02e8fcfeed4001c1f8ba91a9e1f31483c0dffb207002a
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - greenlet>=1 ; (python_full_version < '3.14' and platform_machine == 'AMD64') or (python_full_version < '3.14' and platform_machine == 'WIN32') or (python_full_version < '3.14' and platform_machine == 'aarch64') or (python_full_version < '3.14' and platform_machine == 'amd64') or (python_full_version < '3.14' and platform_machine == 'ppc64le') or (python_full_version < '3.14' and platform_machine == 'win32') or (python_full_version < '3.14' and platform_machine == 'x86_64')
  - typing-extensions>=4.6.0
  - greenlet>=1 ; extra == 'asyncio'
  - mypy>=0.910 ; extra == 'mypy'
  - pyodbc ; extra == 'mssql'
  - pymssql ; extra == 'mssql-pymssql'
  - pyodbc ; extra == 'mssql-pyodbc'
  - mysqlclient>=1.4.0 ; extra == 'mysql'
  - mysql-connector-python ; extra == 'mysql-connector'
  - mariadb>=1.0.1,!=1.1.2,!=1.1.5,!=1.1.10 ; extra == 'mariadb-connector'
  - cx-oracle>=8 ; extra == 'oracle'
  - oracledb>=1.0.1 ; extra == 'oracle-oracledb'
  - psycopg2>=2.7 ; extra == 'postgresql'
  - pg8000>=1.29.1 ; extra == 'postgresql-pg8000'
  - greenlet>=1 ; extra == 'postgresql-asyncpg'
  - asyncpg ; extra == 'postgresql-asyncpg'
  - psycopg2-binary ; extra == 'postgresql-psycopg2binary'
  - psycopg2cffi ; extra == 'postgresql-psycopg2cffi'
  - psycopg>=3.0.7 ; extra == 'postgresql-psycopg'
  - psycopg[binary]>=3.0.7 ; extra == 'postgresql-psycopgbinary'
  - pymysql ; extra == 'pymysql'
  - greenlet>=1 ; extra == 'aiomysql'
  - aiomysql>=0.2.0 ; extra == 'aiomysql'
  - greenlet>=1 ; extra == 'aioodbc'
  - aioodbc ; extra == 'aioodbc'
  - greenlet>=1 ; extra == 'asyncmy'
  - asyncmy>=0.2.3,!=0.2.4,!=0.2.6 ; extra == 'asyncmy'
  - greenlet>=1 ; extra == 'aiosqlite'
  - aiosqlite ; extra == 'aiosqlite'
  - typing-extensions!=******** ; extra == 'aiosqlite'
  - sqlcipher3-binary ; extra == 'sqlcipher'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/a0/72/c97ad430f0b0e78efaf2791342e13ffeafcbb3c06242f01a3bb8fe44f65d/sqlalchemy-2.0.41-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: sqlalchemy
  version: 2.0.41
  sha256: a62448526dd9ed3e3beedc93df9bb6b55a436ed1474db31a2af13b313a70a7e1
  requires_dist:
  - importlib-metadata ; python_full_version < '3.8'
  - greenlet>=1 ; (python_full_version < '3.14' and platform_machine == 'AMD64') or (python_full_version < '3.14' and platform_machine == 'WIN32') or (python_full_version < '3.14' and platform_machine == 'aarch64') or (python_full_version < '3.14' and platform_machine == 'amd64') or (python_full_version < '3.14' and platform_machine == 'ppc64le') or (python_full_version < '3.14' and platform_machine == 'win32') or (python_full_version < '3.14' and platform_machine == 'x86_64')
  - typing-extensions>=4.6.0
  - greenlet>=1 ; extra == 'asyncio'
  - mypy>=0.910 ; extra == 'mypy'
  - pyodbc ; extra == 'mssql'
  - pymssql ; extra == 'mssql-pymssql'
  - pyodbc ; extra == 'mssql-pyodbc'
  - mysqlclient>=1.4.0 ; extra == 'mysql'
  - mysql-connector-python ; extra == 'mysql-connector'
  - mariadb>=1.0.1,!=1.1.2,!=1.1.5,!=1.1.10 ; extra == 'mariadb-connector'
  - cx-oracle>=8 ; extra == 'oracle'
  - oracledb>=1.0.1 ; extra == 'oracle-oracledb'
  - psycopg2>=2.7 ; extra == 'postgresql'
  - pg8000>=1.29.1 ; extra == 'postgresql-pg8000'
  - greenlet>=1 ; extra == 'postgresql-asyncpg'
  - asyncpg ; extra == 'postgresql-asyncpg'
  - psycopg2-binary ; extra == 'postgresql-psycopg2binary'
  - psycopg2cffi ; extra == 'postgresql-psycopg2cffi'
  - psycopg>=3.0.7 ; extra == 'postgresql-psycopg'
  - psycopg[binary]>=3.0.7 ; extra == 'postgresql-psycopgbinary'
  - pymysql ; extra == 'pymysql'
  - greenlet>=1 ; extra == 'aiomysql'
  - aiomysql>=0.2.0 ; extra == 'aiomysql'
  - greenlet>=1 ; extra == 'aioodbc'
  - aioodbc ; extra == 'aioodbc'
  - greenlet>=1 ; extra == 'asyncmy'
  - asyncmy>=0.2.3,!=0.2.4,!=0.2.6 ; extra == 'asyncmy'
  - greenlet>=1 ; extra == 'aiosqlite'
  - aiosqlite ; extra == 'aiosqlite'
  - typing-extensions!=******** ; extra == 'aiosqlite'
  - sqlcipher3-binary ; extra == 'sqlcipher'
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/8b/0c/9d30a4ebeb6db2b25a841afbb80f6ef9a854fc3b41be131d249a977b4959/starlette-0.46.2-py3-none-any.whl
  name: starlette
  version: 0.46.2
  sha256: 595633ce89f8ffa71a015caed34a5b2dc1c0cdb3f0f1fbd1e69339cf2abeec35
  requires_dist:
  - anyio>=3.6.2,<5
  - typing-extensions>=3.10.0 ; python_full_version < '3.10'
  - httpx>=0.27.0,<0.29.0 ; extra == 'full'
  - itsdangerous ; extra == 'full'
  - jinja2 ; extra == 'full'
  - python-multipart>=0.0.18 ; extra == 'full'
  - pyyaml ; extra == 'full'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/a0/4a/97ee6973e3a73c74c8120d59829c3861ea52210667ec3e7a16045c62b64d/structlog-25.4.0-py3-none-any.whl
  name: structlog
  version: 25.4.0
  sha256: fe809ff5c27e557d14e613f45ca441aabda051d119ee5a0102aaba6ce40eed2c
  requires_dist:
  - typing-extensions ; python_full_version < '3.11'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/svt-av1-3.0.2-h5888daf_0.conda
  sha256: fb4b97a3fd259eff4849b2cfe5678ced0c5792b697eb1f7bcd93a4230e90e80e
  md5: 0096882bd623e6cc09e8bf920fc8fb47
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 2750235
  timestamp: 1742907589246
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/svt-av1-3.0.2-h5ad3122_0.conda
  sha256: 6d2ac9e4f68355ba3b42395054a7558b9eb6bcf3d70e91bb99ada1450a74d2f6
  md5: 4fafb3aafa73a875312cb4a1099d2a46
  depends:
  - libgcc >=13
  - libstdcxx >=13
  license: BSD-2-Clause
  license_family: BSD
  purls: []
  size: 1975547
  timestamp: 1742910351387
- conda: https://conda.anaconda.org/conda-forge/linux-64/tbb-2022.1.0-h4ce085d_0.conda
  sha256: b2819dd77faee0ea1f14774b603db33da44c14f7662982d4da4bbe76ac8a8976
  md5: f0afd0c7509f6c1b8d77ee64d7ba64b8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 179639
  timestamp: 1743578685131
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tbb-2022.1.0-hf6e3e71_0.conda
  sha256: 3dea624f5495c4cfe1bd5a35d67f5995c3a4cde42024ec57855ddab502e1ea3c
  md5: 0d08f8f53a51b2dfbcda8ebe3be28103
  depends:
  - libgcc >=13
  - libhwloc >=2.11.2,<2.11.3.0a0
  - libstdcxx >=13
  license: Apache-2.0
  license_family: APACHE
  purls: []
  size: 144738
  timestamp: 1743581521035
- pypi: https://files.pythonhosted.org/packages/e5/30/643397144bfbfec6f6ef821f36f33e57d35946c44a2352d3c9f0ae847619/tenacity-9.1.2-py3-none-any.whl
  name: tenacity
  version: 9.1.2
  sha256: f77bf36710d8b73a50b2dd155c97b870017ad21afe6ab300326b0371b3b05138
  requires_dist:
  - reno ; extra == 'doc'
  - sphinx ; extra == 'doc'
  - pytest ; extra == 'test'
  - tornado>=4.5 ; extra == 'test'
  - typeguard ; extra == 'test'
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/tk-8.6.13-noxft_hd72426e_102.conda
  sha256: a84ff687119e6d8752346d1d408d5cf360dee0badd487a472aa8ddedfdc219e1
  md5: a0116df4f4ed05c303811a837d5b39d8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3285204
  timestamp: 1748387766691
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/tk-8.6.13-noxft_h5688188_102.conda
  sha256: 46e10488e9254092c655257c18fcec0a9864043bdfbe935a9fbf4fb2028b8514
  md5: 2562c9bfd1de3f9c590f0fe53858d85c
  depends:
  - libgcc >=13
  - libzlib >=1.3.1,<2.0a0
  license: TCL
  license_family: BSD
  purls: []
  size: 3342845
  timestamp: 1748393219221
- pypi: https://files.pythonhosted.org/packages/76/42/3efaf858001d2c2913de7f354563e3a3a2f0decae3efe98427125a8f441e/typer-0.16.0-py3-none-any.whl
  name: typer
  version: 0.16.0
  sha256: 1f79bed11d4d02d4310e3c1b7ba594183bcedb0ac73b27a9e5f28f6fb5b98855
  requires_dist:
  - click>=8.0.0
  - typing-extensions>=*******
  - shellingham>=1.3.0
  - rich>=10.11.0
  requires_python: '>=3.7'
- pypi: https://files.pythonhosted.org/packages/69/e0/552843e0d356fbb5256d21449fa957fa4eff3bbc135a74a691ee70c7c5da/typing_extensions-4.14.0-py3-none-any.whl
  name: typing-extensions
  version: 4.14.0
  sha256: a1514509136dd0b477638fc68d6a91497af5076466ad0fa6c338e44e359944af
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/b5/00/d631e67a838026495268c2f6884f3711a15a9a2a96cd244fdaea53b823fb/typing_extensions-4.14.1-py3-none-any.whl
  name: typing-extensions
  version: 4.14.1
  sha256: d1e1e3b58374dc93031d6eda2420a48ea44a36c2b4766a4fdeb3710755731d76
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/17/69/cd203477f944c353c31bade965f880aa1061fd6bf05ded0726ca845b6ff7/typing_inspection-0.4.1-py3-none-any.whl
  name: typing-inspection
  version: 0.4.1
  sha256: 389055682238f53b04f7badcb49b989835495a96700ced5dab2d8feae4b26f51
  requires_dist:
  - typing-extensions>=4.12.0
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/noarch/tzdata-2025b-h78e105d_0.conda
  sha256: 5aaa366385d716557e365f0a4e9c3fca43ba196872abbbe3d56bb610d131e192
  md5: 4222072737ccff51314b5ece9c7d6f5a
  license: LicenseRef-Public-Domain
  purls: []
  size: 122968
  timestamp: 1742727099393
- pypi: https://files.pythonhosted.org/packages/c2/14/e2a54fabd4f08cd7af1c07030603c3356b74da07f7cc056e600436edfa17/tzlocal-5.3.1-py3-none-any.whl
  name: tzlocal
  version: 5.3.1
  sha256: eb1a66c3ef5847adf7a834f1be0800581b683b5608e74f86ecbcef8ab91bb85d
  requires_dist:
  - tzdata ; sys_platform == 'win32'
  - pytest>=4.3 ; extra == 'devenv'
  - pytest-mock>=3.3 ; extra == 'devenv'
  - pytest-cov ; extra == 'devenv'
  - check-manifest ; extra == 'devenv'
  - zest-releaser ; extra == 'devenv'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/a9/99/3ae339466c9183ea5b8ae87b34c0b897eda475d2aec2307cae60e5cd4f29/uritemplate-4.2.0-py3-none-any.whl
  name: uritemplate
  version: 4.2.0
  sha256: 962201ba1c4edcab02e60f9a0d3821e82dfc5d2d6662a21abd533879bdb8a686
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/a7/c2/fe1e52489ae3122415c51f387e221dd0773709bad6c6cdaa599e8a2c5185/urllib3-2.5.0-py3-none-any.whl
  name: urllib3
  version: 2.5.0
  sha256: e6b01673c0fa6a13e374b50871808eb3bf7046c4b125b216f6bf1cc604cff0dc
  requires_dist:
  - brotli>=1.0.9 ; platform_python_implementation == 'CPython' and extra == 'brotli'
  - brotlicffi>=0.8.0 ; platform_python_implementation != 'CPython' and extra == 'brotli'
  - h2>=4,<5 ; extra == 'h2'
  - pysocks>=1.5.6,!=1.5.7,<2.0 ; extra == 'socks'
  - zstandard>=0.18.0 ; extra == 'zstd'
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/f3/40/b1c265d4b2b62b58576588510fc4d1fe60a86319c8de99fd8e9fec617d2c/virtualenv-20.31.2-py3-none-any.whl
  name: virtualenv
  version: 20.31.2
  sha256: 36efd0d9650ee985f0cad72065001e66d49a6f24eb44d98980f630686243cf11
  requires_dist:
  - distlib>=0.3.7,<1
  - filelock>=3.12.2,<4
  - importlib-metadata>=6.6 ; python_full_version < '3.8'
  - platformdirs>=3.9.1,<5
  - furo>=2023.7.26 ; extra == 'docs'
  - proselint>=0.13 ; extra == 'docs'
  - sphinx>=7.1.2,!=7.3 ; extra == 'docs'
  - sphinx-argparse>=0.4 ; extra == 'docs'
  - sphinxcontrib-towncrier>=0.2.1a0 ; extra == 'docs'
  - towncrier>=23.6 ; extra == 'docs'
  - covdefaults>=2.3 ; extra == 'test'
  - coverage-enable-subprocess>=1 ; extra == 'test'
  - coverage>=7.2.7 ; extra == 'test'
  - flaky>=3.7 ; extra == 'test'
  - packaging>=23.1 ; extra == 'test'
  - pytest-env>=0.8.2 ; extra == 'test'
  - pytest-freezer>=0.4.8 ; (python_full_version >= '3.13' and platform_python_implementation == 'CPython' and sys_platform == 'win32' and extra == 'test') or (platform_python_implementation == 'GraalVM' and extra == 'test') or (platform_python_implementation == 'PyPy' and extra == 'test')
  - pytest-mock>=3.11.1 ; extra == 'test'
  - pytest-randomly>=3.12 ; extra == 'test'
  - pytest-timeout>=2.1 ; extra == 'test'
  - pytest>=7.4 ; extra == 'test'
  - setuptools>=68 ; extra == 'test'
  - time-machine>=2.10 ; platform_python_implementation == 'CPython' and extra == 'test'
  requires_python: '>=3.8'
- conda: https://conda.anaconda.org/conda-forge/linux-64/wayland-1.23.1-h3e06ad9_1.conda
  sha256: 73d809ec8056c2f08e077f9d779d7f4e4c2b625881cad6af303c33dc1562ea01
  md5: a37843723437ba75f42c9270ffe800b1
  depends:
  - __glibc >=2.17,<3.0.a0
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 321099
  timestamp: 1745806602179
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/wayland-1.24.0-h698ed42_0.conda
  sha256: 2a58c43ae7a618a329705df8406420ac89c9093386c5ca356ae7f2291f012e58
  md5: 2a57237cee70cb13c402af1ef6f8e5f6
  depends:
  - libexpat >=2.7.0,<3.0a0
  - libffi >=3.4.6,<3.5.0a0
  - libgcc >=13
  - libstdcxx >=13
  license: MIT
  purls: []
  size: 332236
  timestamp: 1751818023302
- conda: https://conda.anaconda.org/conda-forge/noarch/wayland-protocols-1.45-hd8ed1ab_0.conda
  sha256: ****************************************************************
  md5: 6db9be3b67190229479780eeeee1b35b
  license: MIT
  license_family: MIT
  purls: []
  size: 138011
  timestamp: 1749836220507
- conda: https://conda.anaconda.org/conda-forge/linux-64/x264-1!164.3095-h166bdaf_2.tar.bz2
  sha256: 175315eb3d6ea1f64a6ce470be00fa2ee59980108f246d3072ab8b977cb048a5
  md5: 6c99772d483f566d59e25037fea2c4b1
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 897548
  timestamp: 1660323080555
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x264-1!164.3095-h4e544f5_2.tar.bz2
  sha256: b48f150db8c052c197691c9d76f59e252d3a7f01de123753d51ebf2eed1cf057
  md5: 0efaf807a0b5844ce5f605bd9b668281
  depends:
  - libgcc-ng >=12
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 1000661
  timestamp: 1660324722559
- conda: https://conda.anaconda.org/conda-forge/linux-64/x265-3.5-h924138e_3.tar.bz2
  sha256: 76c7405bcf2af639971150f342550484efac18219c0203c5ee2e38b8956fe2a0
  md5: e7f6ed84d4623d52ee581325c1587a6b
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 3357188
  timestamp: 1646609687141
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/x265-3.5-hdd96247_3.tar.bz2
  sha256: cb2227f2441499900bdc0168eb423d7b2056c8fd5a3541df4e2d05509a88c668
  md5: 786853760099c74a1d4f0da98dd67aea
  depends:
  - libgcc-ng >=10.3.0
  - libstdcxx-ng >=10.3.0
  license: GPL-2.0-or-later
  license_family: GPL
  purls: []
  size: 1018181
  timestamp: 1646610147365
- conda: https://conda.anaconda.org/conda-forge/linux-64/xkeyboard-config-2.45-hb9d3cd8_0.conda
  sha256: a5d4af601f71805ec67403406e147c48d6bad7aaeae92b0622b7e2396842d3fe
  md5: 397a013c2dc5145a70737871aaa87e98
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392406
  timestamp: 1749375847832
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xkeyboard-config-2.45-h86ecc28_0.conda
  sha256: 730ff2f6fbfecce94db54bbf3f1ae0ce79c54b6abc089f8a65a041525228d454
  md5: 01251d1503a253e39be4fa9bcf447d63
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.12,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 392754
  timestamp: 1749375869926
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libice-1.1.2-hb9d3cd8_0.conda
  sha256: c12396aabb21244c212e488bbdc4abcdef0b7404b15761d9329f5a4a39113c4b
  md5: fb901ff28063514abb6046c9ec2c4a45
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 58628
  timestamp: 1734227592886
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libice-1.1.2-h86ecc28_0.conda
  sha256: a2ba1864403c7eb4194dacbfe2777acf3d596feae43aada8d1b478617ce45031
  md5: c8d8ec3e00cd0fd8a231789b91a7c5b7
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 60433
  timestamp: 1734229908988
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libsm-1.2.6-he73a12e_0.conda
  sha256: 277841c43a39f738927145930ff963c5ce4c4dacf66637a3d95d802a64173250
  md5: 1c74ff8c35dcadf952a16f752ca5aa49
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 27590
  timestamp: 1741896361728
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libsm-1.2.6-h0808dbd_0.conda
  sha256: b86a819cd16f90c01d9d81892155126d01555a20dabd5f3091da59d6309afd0a
  md5: 2d1409c50882819cb1af2de82e2b7208
  depends:
  - libgcc >=13
  - libuuid >=2.38.1,<3.0a0
  - xorg-libice >=1.1.2,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 28701
  timestamp: 1741897678254
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libx11-1.8.12-h4f16b4b_0.conda
  sha256: 51909270b1a6c5474ed3978628b341b4d4472cd22610e5f22b506855a5e20f67
  md5: db038ce880f100acc74dba10302b5630
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 835896
  timestamp: 1741901112627
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libx11-1.8.12-hca56bd8_0.conda
  sha256: 452977d8ad96f04ec668ba74f46e70a53e00f99c0e0307956aeca75894c8131d
  md5: 3df132f0048b9639bc091ef22937c111
  depends:
  - libgcc >=13
  - libxcb >=1.17.0,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 864850
  timestamp: 1741901264068
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxau-1.0.12-hb9d3cd8_0.conda
  sha256: ed10c9283974d311855ae08a16dfd7e56241fac632aec3b92e3cfe73cff31038
  md5: f6ebe2cb3f82ba6c057dde5d9debe4f7
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 14780
  timestamp: 1734229004433
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxau-1.0.12-h86ecc28_0.conda
  sha256: 7829a0019b99ba462aece7592d2d7f42e12d12ccd3b9614e529de6ddba453685
  md5: d5397424399a66d33c80b1f2345a36a6
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 15873
  timestamp: 1734230458294
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxcursor-1.2.3-hb9d3cd8_0.conda
  sha256: 832f538ade441b1eee863c8c91af9e69b356cd3e9e1350fff4fe36cc573fc91a
  md5: 2ccd714aa2242315acaf0a67faea780b
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 32533
  timestamp: 1730908305254
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxcursor-1.2.3-h86ecc28_0.conda
  sha256: c5d3692520762322a9598e7448492309f5ee9d8f3aff72d787cf06e77c42507f
  md5: f2054759c2203d12d0007005e1f1296d
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  - xorg-libxfixes >=6.0.1,<7.0a0
  - xorg-libxrender >=0.9.11,<0.10.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 34596
  timestamp: 1730908388714
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxdmcp-1.1.5-hb9d3cd8_0.conda
  sha256: 6b250f3e59db07c2514057944a3ea2044d6a8cdde8a47b6497c254520fade1ee
  md5: 8035c64cb77ed555e3f150b7b3972480
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 19901
  timestamp: 1727794976192
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxdmcp-1.1.5-h57736b2_0.conda
  sha256: efcc150da5926cf244f757b8376d96a4db78bc15b8d90ca9f56ac6e75755971f
  md5: 25a5a7b797fe6e084e04ffe2db02fc62
  depends:
  - libgcc >=13
  license: MIT
  license_family: MIT
  purls: []
  size: 20615
  timestamp: 1727796660574
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxext-1.3.6-hb9d3cd8_0.conda
  sha256: da5dc921c017c05f38a38bd75245017463104457b63a1ce633ed41f214159c14
  md5: febbab7d15033c913d53c7a2c102309d
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 50060
  timestamp: 1727752228921
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxext-1.3.6-h57736b2_0.conda
  sha256: 8e216b024f52e367463b4173f237af97cf7053c77d9ce3e958bc62473a053f71
  md5: bd1e86dd8aa3afd78a4bfdb4ef918165
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 50746
  timestamp: 1727754268156
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxfixes-6.0.1-hb9d3cd8_0.conda
  sha256: 2fef37e660985794617716eb915865ce157004a4d567ed35ec16514960ae9271
  md5: 4bdb303603e9821baf5fe5fdff1dc8f8
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 19575
  timestamp: 1727794961233
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxfixes-6.0.1-h57736b2_0.conda
  sha256: f5c71e0555681a82a65c483374b91d91b2cb9a9903b3a22ddc00f36719fce549
  md5: 78f8715c002cc66991d7c11e3cf66039
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.9,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 20289
  timestamp: 1727796500830
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxrender-0.9.12-hb9d3cd8_0.conda
  sha256: 044c7b3153c224c6cedd4484dd91b389d2d7fd9c776ad0f4a34f099b3389f4a1
  md5: 96d57aba173e878a2089d5638016dc5e
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 33005
  timestamp: 1734229037766
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/xorg-libxrender-0.9.12-h86ecc28_0.conda
  sha256: ffd77ee860c9635a28cfda46163dcfe9224dc6248c62404c544ae6b564a0be1f
  md5: ae2c2dd0e2d38d249887727db2af960e
  depends:
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 33649
  timestamp: 1734229123157
- conda: https://conda.anaconda.org/conda-forge/linux-64/xorg-libxscrnsaver-1.2.4-hb9d3cd8_0.conda
  sha256: 58e8fc1687534124832d22e102f098b5401173212ac69eb9fd96b16a3e2c8cb2
  md5: 303f7a0e9e0cd7d250bb6b952cecda90
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - xorg-libx11 >=1.8.10,<2.0a0
  - xorg-libxext >=1.3.6,<2.0a0
  license: MIT
  license_family: MIT
  purls: []
  size: 14412
  timestamp: 1727899730073
- pypi: https://files.pythonhosted.org/packages/a3/25/35afe384e31115a1a801fbcf84012d7a066d89035befae7c5d4284df1e03/yarl-1.20.1-cp313-cp313-manylinux_2_17_aarch64.manylinux2014_aarch64.whl
  name: yarl
  version: 1.20.1
  sha256: 49bdd1b8e00ce57e68ba51916e4bb04461746e794e7c4d4bbc42ba2f18297691
  requires_dist:
  - idna>=2.0
  - multidict>=4.0
  - propcache>=0.2.1
  requires_python: '>=3.9'
- pypi: https://files.pythonhosted.org/packages/af/44/46407d7f7a56e9a85a4c207724c9f2c545c060380718eea9088f222ba697/yarl-1.20.1-cp313-cp313-manylinux_2_17_x86_64.manylinux2014_x86_64.whl
  name: yarl
  version: 1.20.1
  sha256: d1a4fbb50e14396ba3d375f68bfe02215d8e7bc3ec49da8341fe3157f59d2ff5
  requires_dist:
  - idna>=2.0
  - multidict>=4.0
  - propcache>=0.2.1
  requires_python: '>=3.9'
- conda: https://conda.anaconda.org/conda-forge/linux-64/zstd-1.5.7-hb8e6e7a_2.conda
  sha256: a4166e3d8ff4e35932510aaff7aa90772f84b4d07e9f6f83c614cba7ceefe0eb
  md5: 6432cb5d4ac0046c3ac0a8a0f95842f9
  depends:
  - __glibc >=2.17,<3.0.a0
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 567578
  timestamp: 1742433379869
- conda: https://conda.anaconda.org/conda-forge/linux-aarch64/zstd-1.5.7-hbcf94c1_2.conda
  sha256: 0812e7b45f087cfdd288690ada718ce5e13e8263312e03b643dd7aa50d08b51b
  md5: 5be90c5a3e4b43c53e38f50a85e11527
  depends:
  - libgcc >=13
  - libstdcxx >=13
  - libzlib >=1.3.1,<2.0a0
  license: BSD-3-Clause
  license_family: BSD
  purls: []
  size: 551176
  timestamp: 1742433378347
