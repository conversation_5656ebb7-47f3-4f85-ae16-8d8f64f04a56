#!/usr/bin/env python3
"""
Test script to demonstrate the improved progress reporting system.
"""

import asyncio
import sys
from pathlib import Path
from unittest.mock import AsyncMock, patch

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from video_generator import LoFiVideoGenerator
from music_sources.base import Track, LoFiStyle


async def test_progress_reporting():
    """Test that progress callbacks are called with detailed messages."""
    print("🧪 Testing improved progress reporting...")
    print("=" * 50)
    
    # Capture progress messages
    progress_messages = []
    
    async def capture_progress(message: str):
        """Capture progress messages for testing."""
        progress_messages.append(message)
        print(f"📊 Progress: {message}")
    
    # Mock all the external dependencies
    with patch('asyncio.create_subprocess_exec') as mock_subprocess, \
         patch('pathlib.Path.exists', return_value=True), \
         patch('builtins.open', create=True) as mock_open:
        
        # Mock successful subprocess execution
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (b'{"streams": [{"codec_type": "video", "duration": "10.0"}]}', b'')
        mock_subprocess.return_value = mock_process
        
        # Mock file operations
        mock_file = AsyncMock()
        mock_open.return_value.__enter__.return_value = mock_file
        
        video_generator = LoFiVideoGenerator()
        
        # Create dummy track
        dummy_track = Track(
            id="test_track",
            title="Test Lo-Fi Track",
            artist="Test Artist",
            duration=10.0,
            preview_url="http://example.com/test.mp3",
            source="test"
        )
        
        # Test single track video generation with progress
        print("\n🎬 Testing single track video generation...")
        result = await video_generator.generate_video(
            track=dummy_track,
            audio_data=b"fake_audio_data",
            duration=10.0,
            style=LoFiStyle.CHILL,
            output_filename="test_output.mp4",
            progress_callback=capture_progress
        )
        
        print(f"\n📊 Captured {len(progress_messages)} progress messages:")
        for i, message in enumerate(progress_messages, 1):
            print(f"  {i}. {message}")
        
        # Verify we got detailed progress messages
        expected_keywords = [
            "Preparing video",
            "Saving audio",
            "Creating",
            "Combining"
        ]
        
        found_keywords = []
        for keyword in expected_keywords:
            for message in progress_messages:
                if keyword.lower() in message.lower():
                    found_keywords.append(keyword)
                    break
        
        print(f"\n✅ Found {len(found_keywords)}/{len(expected_keywords)} expected progress types:")
        for keyword in found_keywords:
            print(f"  ✓ {keyword}")
        
        missing_keywords = set(expected_keywords) - set(found_keywords)
        if missing_keywords:
            print(f"\n⚠️ Missing progress types:")
            for keyword in missing_keywords:
                print(f"  ✗ {keyword}")
        
        # Test multi-track video generation
        print("\n🎵 Testing multi-track video generation...")
        progress_messages.clear()
        
        tracks = [dummy_track, dummy_track]  # Two tracks
        audio_data_list = [b"fake_audio_data_1", b"fake_audio_data_2"]
        
        result = await video_generator.generate_video_from_multiple_tracks(
            tracks=tracks,
            audio_data_list=audio_data_list,
            duration=20.0,
            style=LoFiStyle.UPBEAT,
            output_filename="test_multi_output.mp4",
            progress_callback=capture_progress
        )
        
        print(f"\n📊 Multi-track captured {len(progress_messages)} progress messages:")
        for i, message in enumerate(progress_messages, 1):
            print(f"  {i}. {message}")
        
        # Verify multi-track specific messages
        multi_track_keywords = [
            "multi-track",
            "Concatenating",
            "tracks"
        ]
        
        found_multi_keywords = []
        for keyword in multi_track_keywords:
            for message in progress_messages:
                if keyword.lower() in message.lower():
                    found_multi_keywords.append(keyword)
                    break
        
        print(f"\n✅ Found {len(found_multi_keywords)}/{len(multi_track_keywords)} multi-track progress types:")
        for keyword in found_multi_keywords:
            print(f"  ✓ {keyword}")
        
        return len(progress_messages) > 0 and len(found_keywords) >= 3


async def test_background_video_progress():
    """Test progress reporting for background video processing."""
    print("\n🎬 Testing background video progress reporting...")
    print("=" * 50)
    
    progress_messages = []
    
    async def capture_progress(message: str):
        progress_messages.append(message)
        print(f"📊 Progress: {message}")
    
    with patch('asyncio.create_subprocess_exec') as mock_subprocess, \
         patch('pathlib.Path.exists', return_value=True):
        
        # Mock video info response
        mock_process = AsyncMock()
        mock_process.returncode = 0
        mock_process.communicate.return_value = (
            b'{"streams": [{"codec_type": "video", "duration": "15.0"}]}', 
            b''
        )
        mock_subprocess.return_value = mock_process
        
        video_generator = LoFiVideoGenerator()
        
        # Test background video preparation
        result = await video_generator._prepare_background_video(
            "/fake/background.mp4", 
            10.0, 
            capture_progress
        )
        
        print(f"\n📊 Background video captured {len(progress_messages)} progress messages:")
        for i, message in enumerate(progress_messages, 1):
            print(f"  {i}. {message}")
        
        # Check for background-specific progress messages
        background_keywords = [
            "Analyzing",
            "background video",
            "Trimming",
            "Looping"
        ]
        
        found_bg_keywords = []
        for keyword in background_keywords:
            for message in progress_messages:
                if keyword.lower() in message.lower():
                    found_bg_keywords.append(keyword)
                    break
        
        print(f"\n✅ Found {len(found_bg_keywords)} background video progress types:")
        for keyword in found_bg_keywords:
            print(f"  ✓ {keyword}")
        
        return len(progress_messages) > 0


async def main():
    """Run all progress reporting tests."""
    print("🎬 Lo-Fi Video Generator - Progress Reporting Test")
    print("=" * 60)
    
    try:
        # Test basic progress reporting
        success1 = await test_progress_reporting()
        
        # Test background video progress
        success2 = await test_background_video_progress()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("✅ ALL TESTS PASSED!")
            print("📊 Progress reporting is working correctly")
            print("🎯 Users will now see detailed real-time progress")
            return True
        else:
            print("❌ SOME TESTS FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
