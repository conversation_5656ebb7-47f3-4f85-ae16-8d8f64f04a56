#!/usr/bin/env python3
"""
Test script to verify that FFmpeg commands for background video processing
are constructed correctly to strip audio.
"""

import sys
from pathlib import Path
from unittest.mock import AsyncMock, patch, MagicMock
import asyncio

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from video_generator import LoFiVideoGenerator
from music_sources.base import Track, LoFiStyle


class FFmpegCommandCapture:
    """Helper class to capture FFmpeg commands without executing them."""
    
    def __init__(self):
        self.commands = []
        self.return_code = 0
        self.stdout = b""
        self.stderr = b""
    
    async def create_subprocess_exec(self, *args, **kwargs):
        """Mock subprocess execution that captures commands."""
        self.commands.append(list(args))
        
        # Create a mock process
        mock_process = AsyncMock()
        mock_process.returncode = self.return_code
        mock_process.communicate.return_value = (self.stdout, self.stderr)
        
        return mock_process


def test_background_video_preparation_commands():
    """Test that background video preparation strips audio correctly."""
    print("🧪 Testing background video preparation FFmpeg commands...")
    
    # Test data
    test_video_info = {
        'streams': [
            {'codec_type': 'video', 'duration': '15.0'},
            {'codec_type': 'audio', 'duration': '15.0'}
        ],
        'format': {'duration': '15.0'}
    }
    
    async def run_test():
        capture = FFmpegCommandCapture()
        
        # Mock the video info response
        import json
        capture.stdout = json.dumps(test_video_info).encode()
        
        with patch('asyncio.create_subprocess_exec', capture.create_subprocess_exec), \
             patch('pathlib.Path.exists', return_value=True):
            video_generator = LoFiVideoGenerator()

            # Test case 1: Background video longer than target (trimming)
            print("\n📹 Testing background video trimming (longer than target)...")
            result = await video_generator._prepare_background_video(
                "/fake/path/background.mp4", 10.0
            )
            
            # Should have 2 commands: ffprobe + ffmpeg
            assert len(capture.commands) == 2, f"Expected 2 commands, got {len(capture.commands)}"
            
            ffprobe_cmd = capture.commands[0]
            ffmpeg_cmd = capture.commands[1]
            
            # Check ffprobe command
            assert ffprobe_cmd[0] == 'ffprobe', "First command should be ffprobe"
            
            # Check ffmpeg command for audio stripping
            assert ffmpeg_cmd[0] == 'ffmpeg', "Second command should be ffmpeg"
            assert '-an' in ffmpeg_cmd, "FFmpeg command should include -an flag to strip audio"
            assert '-c:a' not in ffmpeg_cmd, "FFmpeg command should not include audio codec"
            
            print("✅ Trimming command correctly strips audio with -an flag")
            
            # Reset for next test
            capture.commands = []
            
            # Test case 2: Background video shorter than target (looping)
            print("\n🔄 Testing background video looping (shorter than target)...")
            
            # Mock shorter video
            short_video_info = {
                'streams': [
                    {'codec_type': 'video', 'duration': '5.0'},
                    {'codec_type': 'audio', 'duration': '5.0'}
                ],
                'format': {'duration': '5.0'}
            }
            capture.stdout = json.dumps(short_video_info).encode()
            
            result = await video_generator._prepare_background_video(
                "/fake/path/short_background.mp4", 10.0
            )
            
            # Should have 2 commands: ffprobe + ffmpeg
            assert len(capture.commands) == 2, f"Expected 2 commands, got {len(capture.commands)}"
            
            ffprobe_cmd = capture.commands[0]
            ffmpeg_cmd = capture.commands[1]
            
            # Check ffmpeg command for audio stripping in loop mode
            assert ffmpeg_cmd[0] == 'ffmpeg', "Second command should be ffmpeg"
            assert '-an' in ffmpeg_cmd, "FFmpeg command should include -an flag to strip audio"
            assert '-stream_loop' in ffmpeg_cmd, "FFmpeg command should include -stream_loop for looping"
            assert '-c:a' not in ffmpeg_cmd, "FFmpeg command should not include audio codec"
            
            print("✅ Looping command correctly strips audio with -an flag")
            
            return True
    
    return asyncio.run(run_test())


def test_final_video_combination_commands():
    """Test that final video combination uses correct audio mapping."""
    print("\n🧪 Testing final video combination FFmpeg commands...")
    
    async def run_test():
        capture = FFmpegCommandCapture()
        
        with patch('asyncio.create_subprocess_exec', capture.create_subprocess_exec):
            video_generator = LoFiVideoGenerator()
            
            # Create dummy track
            dummy_track = Track(
                id="test_track",
                title="Test Track",
                artist="Test Artist",
                duration=10.0,
                preview_url="http://example.com/test.mp3",
                source="test"
            )
            
            # Test single track combination
            print("\n🎵 Testing single track audio/visual combination...")
            result = await video_generator._combine_audio_visual(
                Path("/fake/audio.mp3"),
                Path("/fake/visual.mp4"),
                Path("/fake/output.mp4"),
                10.0,
                dummy_track,
                LoFiStyle.CHILL
            )
            
            assert len(capture.commands) == 1, f"Expected 1 command, got {len(capture.commands)}"
            ffmpeg_cmd = capture.commands[0]
            
            # Check for correct audio/video mapping
            assert ffmpeg_cmd[0] == 'ffmpeg', "Command should be ffmpeg"
            assert '-map' in ffmpeg_cmd, "Command should include mapping"
            
            # Find mapping indices
            map_indices = [i for i, arg in enumerate(ffmpeg_cmd) if arg == '-map']
            assert len(map_indices) == 2, "Should have exactly 2 -map arguments"
            
            # Check video mapping (should be from input 0)
            video_map = ffmpeg_cmd[map_indices[0] + 1]
            assert video_map == '0:v', f"Video should be mapped from input 0, got {video_map}"
            
            # Check audio mapping (should be from input 1)
            audio_map = ffmpeg_cmd[map_indices[1] + 1]
            assert audio_map == '1:a', f"Audio should be mapped from input 1, got {audio_map}"
            
            print("✅ Single track combination correctly maps video from input 0 and audio from input 1")
            
            # Reset for multi-track test
            capture.commands = []
            
            # Test multi-track combination
            print("\n🎵 Testing multi-track audio/visual combination...")
            result = await video_generator._combine_audio_visual_multi_track(
                Path("/fake/audio.mp3"),
                Path("/fake/visual.mp4"),
                Path("/fake/output.mp4"),
                10.0,
                [dummy_track],
                LoFiStyle.CHILL
            )
            
            assert len(capture.commands) == 1, f"Expected 1 command, got {len(capture.commands)}"
            ffmpeg_cmd = capture.commands[0]
            
            # Check for correct audio/video mapping
            assert ffmpeg_cmd[0] == 'ffmpeg', "Command should be ffmpeg"
            assert '-map' in ffmpeg_cmd, "Command should include mapping"
            
            # Find mapping indices
            map_indices = [i for i, arg in enumerate(ffmpeg_cmd) if arg == '-map']
            assert len(map_indices) == 2, "Should have exactly 2 -map arguments"
            
            # Check video mapping (should be from input 0)
            video_map = ffmpeg_cmd[map_indices[0] + 1]
            assert video_map == '0:v', f"Video should be mapped from input 0, got {video_map}"
            
            # Check audio mapping (should be from input 1)
            audio_map = ffmpeg_cmd[map_indices[1] + 1]
            assert audio_map == '1:a', f"Audio should be mapped from input 1, got {audio_map}"
            
            print("✅ Multi-track combination correctly maps video from input 0 and audio from input 1")
            
            return True
    
    return asyncio.run(run_test())


def main():
    """Run all FFmpeg command tests."""
    print("🎬 Lo-Fi Video Generator - FFmpeg Command Verification")
    print("=" * 60)
    
    try:
        # Test background video preparation
        success1 = test_background_video_preparation_commands()
        
        # Test final video combination
        success2 = test_final_video_combination_commands()
        
        print("\n" + "=" * 60)
        if success1 and success2:
            print("✅ ALL TESTS PASSED!")
            print("🔇 Background video audio will be properly stripped")
            print("🎵 Only lo-fi track audio will be in final videos")
            return True
        else:
            print("❌ SOME TESTS FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ TEST ERROR: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
