#!/usr/bin/env python3
"""
Test script to verify that background videos have their audio stripped correctly.
This test creates a simple background video with audio and verifies that the
final output only contains the lo-fi track audio.
"""

import asyncio
import tempfile
import subprocess
import json
from pathlib import Path
import sys
import os

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from video_generator import LoFiVideoGenerator
from music_sources.base import Track, LoFiStyle


async def create_test_background_video_with_audio(output_path: Path, duration: float = 10.0) -> bool:
    """Create a test background video that contains audio."""
    cmd = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', f'testsrc=duration={duration}:size=320x240:rate=30',  # Test video pattern
        '-f', 'lavfi', 
        '-i', f'sine=frequency=1000:duration={duration}',  # Test audio tone
        '-c:v', 'libx264',
        '-c:a', 'aac',
        '-shortest',
        str(output_path)
    ]
    
    try:
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0 and output_path.exists():
            print(f"✅ Created test background video with audio: {output_path}")
            return True
        else:
            print(f"❌ Failed to create test background video: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating test background video: {e}")
        return False


async def create_test_audio_track(output_path: Path, duration: float = 10.0) -> bool:
    """Create a test lo-fi audio track."""
    cmd = [
        'ffmpeg', '-y',
        '-f', 'lavfi',
        '-i', f'sine=frequency=440:duration={duration}',  # Different frequency for lo-fi track
        '-c:a', 'mp3',
        str(output_path)
    ]
    
    try:
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0 and output_path.exists():
            print(f"✅ Created test lo-fi audio track: {output_path}")
            return True
        else:
            print(f"❌ Failed to create test audio track: {stderr.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ Error creating test audio track: {e}")
        return False


async def analyze_video_audio_streams(video_path: Path) -> dict:
    """Analyze the audio streams in a video file."""
    cmd = [
        'ffprobe', '-v', 'quiet', '-print_format', 'json',
        '-show_streams', '-select_streams', 'a',
        str(video_path)
    ]
    
    try:
        process = await asyncio.create_subprocess_exec(
            *cmd,
            stdout=asyncio.subprocess.PIPE,
            stderr=asyncio.subprocess.PIPE
        )
        
        stdout, stderr = await process.communicate()
        
        if process.returncode == 0:
            return json.loads(stdout.decode())
        else:
            print(f"❌ Failed to analyze video: {stderr.decode()}")
            return {}
            
    except Exception as e:
        print(f"❌ Error analyzing video: {e}")
        return {}


async def test_background_video_audio_stripping():
    """Test that background video audio is properly stripped."""
    print("🧪 Testing background video audio stripping...")
    print("=" * 50)
    
    # Check if FFmpeg is available
    try:
        subprocess.run(['ffmpeg', '-version'], capture_output=True, check=True)
        print("✅ FFmpeg is available")
    except (subprocess.CalledProcessError, FileNotFoundError):
        print("❌ FFmpeg not found. Please install FFmpeg to run this test.")
        return False
    
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        
        # Create test files
        background_video = temp_path / "background_with_audio.mp4"
        lofi_audio = temp_path / "lofi_track.mp3"
        final_video = temp_path / "final_output.mp4"
        
        # Step 1: Create background video with audio (1000Hz tone)
        print("\n📹 Creating test background video with audio...")
        if not await create_test_background_video_with_audio(background_video, 5.0):
            return False
        
        # Step 2: Create lo-fi audio track (440Hz tone)
        print("\n🎵 Creating test lo-fi audio track...")
        if not await create_test_audio_track(lofi_audio, 5.0):
            return False
        
        # Step 3: Verify background video has audio
        print("\n🔍 Analyzing background video audio streams...")
        bg_analysis = await analyze_video_audio_streams(background_video)
        if not bg_analysis.get('streams'):
            print("❌ Background video should have audio streams")
            return False
        print(f"✅ Background video has {len(bg_analysis['streams'])} audio stream(s)")
        
        # Step 4: Use video generator to process the background video
        print("\n⚙️  Processing background video through video generator...")
        video_generator = LoFiVideoGenerator()
        
        # Create a dummy track
        dummy_track = Track(
            id="test_track",
            title="Test Lo-Fi Track",
            artist="Test Artist",
            duration=5.0,
            url="http://example.com/test",
            preview_url="http://example.com/test.mp3",
            license="test",
            source="test"
        )
        
        # Read the lo-fi audio data
        with open(lofi_audio, 'rb') as f:
            audio_data = f.read()
        
        # Generate video with background
        result_path = await video_generator.generate_video(
            track=dummy_track,
            audio_data=audio_data,
            duration=5.0,
            style=LoFiStyle.CHILL,
            output_filename=str(final_video),
            background_video=str(background_video)
        )
        
        if not result_path or not result_path.exists():
            print("❌ Video generation failed")
            return False
        
        print(f"✅ Video generated successfully: {result_path}")
        
        # Step 5: Analyze final video audio
        print("\n🔍 Analyzing final video audio streams...")
        final_analysis = await analyze_video_audio_streams(result_path)
        
        if not final_analysis.get('streams'):
            print("❌ Final video should have audio streams")
            return False
        
        audio_streams = final_analysis['streams']
        print(f"✅ Final video has {len(audio_streams)} audio stream(s)")
        
        # Step 6: Verify audio characteristics
        # The final video should only have the lo-fi track audio, not the background audio
        if len(audio_streams) == 1:
            print("✅ Final video has exactly one audio stream (as expected)")
            print("✅ Background video audio was successfully stripped!")
            return True
        else:
            print(f"❌ Final video has {len(audio_streams)} audio streams, expected 1")
            print("❌ Background video audio may not have been stripped properly")
            return False


async def main():
    """Run the background video audio stripping test."""
    print("🎬 Lo-Fi Video Generator - Background Audio Stripping Test")
    print("=" * 60)
    
    success = await test_background_video_audio_stripping()
    
    print("\n" + "=" * 60)
    if success:
        print("✅ TEST PASSED: Background video audio stripping works correctly!")
        print("🔇 Background videos will not interfere with lo-fi track audio.")
    else:
        print("❌ TEST FAILED: Background video audio stripping needs attention.")
        print("⚠️  Background videos may interfere with lo-fi track audio.")
    
    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
